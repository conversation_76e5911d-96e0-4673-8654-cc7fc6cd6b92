# 通用聚合分页查询框架使用指南

## 概述

通用聚合分页查询框架是一个基于策略模式的跨数据源聚合查询解决方案，旨在解决多数据源分页查询的复杂性，提供统一、高效、可扩展的查询能力。

## 核心特性

### 1. 跨数据源聚合
- 支持多个数据源的数据聚合
- 统一的分页计算算法
- 自动处理数据源间的偏移量计算

### 2. 策略模式设计
- 每个数据源对应一个查询策略
- 策略可独立开发和测试
- 支持动态策略注册和管理

### 3. 灵活的查询条件
- 统一的查询请求模型
- 支持关键词、分类、状态等多种过滤条件
- 可扩展的参数体系

### 4. 高性能优化
- 智能的数据范围计算
- 避免不必要的数据库查询
- 支持查询缓存（可选）

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    AggregationPagingService                 │
│                        (服务门面)                            │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                AggregationPagingEngine                      │
│                   (聚合引擎)                                 │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              AggregationStrategyManager                     │
│                  (策略管理器)                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼──────┐ ┌────▼────┐ ┌──────▼──────┐
│   Strategy1  │ │Strategy2│ │  Strategy3  │
│   (索骥)     │ │ (CDP)   │ │  (通用)     │
└──────────────┘ └─────────┘ └─────────────┘
```

## 核心组件

### 1. AggregationPagingRequest
统一的查询请求模型，包含所有查询条件和分页参数。

```java
AggregationPagingRequest request = new AggregationPagingRequest()
    .setSceneName("feature_query")
    .setKeyword("用户")
    .setOffset(0)
    .setLimit(20)
    .setOrderBy("create_time")
    .addExtParam("onlyOnline", true);
```

### 2. AggregationStrategy
策略接口，定义了每个数据源需要实现的方法。

```java
public interface AggregationStrategy<T> {
    String getStrategyName();
    String getDataSource();
    int getPriority();
    boolean supports(AggregationPagingRequest request);
    long count(AggregationPagingRequest request);
    List<T> queryByPage(AggregationPagingRequest request);
}
```

### 3. AggregationPagingEngine
聚合查询引擎，负责执行跨数据源的分页查询逻辑。

### 4. AggregationStrategyManager
策略管理器，负责策略的注册、发现和选择。

## 使用方法

### 1. 基本查询

```java
@Autowired
private AggregationPagingService aggregationService;

public void basicQuery() {
    AggregationPagingRequest request = new AggregationPagingRequest()
        .setSceneName("general_query")
        .setKeyword("特征")
        .setOffset(0)
        .setLimit(20);
    
    AggregationPagingResponse<AggregationQueryResultDTO> response = 
        aggregationService.query(request);
    
    System.out.println("总数: " + response.getTotal());
    System.out.println("当前页数量: " + response.getSize());
}
```

### 2. 指定策略查询

```java
public void specificStrategyQuery() {
    AggregationPagingRequest request = new AggregationPagingRequest()
        .setKeyword("标签")
        .setOffset(0)
        .setLimit(10);
    
    List<String> strategies = Arrays.asList("CdpAggregateQueryStrategyImpl");
    AggregationPagingResponse<AggregationQueryResultDTO> response = 
        aggregationService.queryWithStrategies(request, strategies);
}
```

### 3. 单一数据源查询

```java
public void singleDataSourceQuery() {
    AggregationPagingRequest request = new AggregationPagingRequest()
        .setSceneName("general_query")
        .setCategoryId(1001);
    
    AggregationPagingResponse<AggregationQueryResultDTO> response = 
        aggregationService.queryFromDataSource(request, "cdp_label");
}
```

## 实现自定义策略

### 1. 继承抽象策略类

```java
@Component
public class MyCustomStrategy extends AbstractAggregationStrategy<MyResultDTO, MyQueryParam> {
    
    public MyCustomStrategy() {
        super(new MyParameterConverter());
    }
    
    @Override
    public String getDataSource() {
        return "my_datasource";
    }
    
    @Override
    public int getPriority() {
        return 100;
    }
    
    @Override
    protected long doCount(MyQueryParam queryParam) {
        // 实现计数逻辑
        return myDao.count(queryParam);
    }
    
    @Override
    protected List<MyResultDTO> doQueryByPage(MyQueryParam queryParam) {
        // 实现分页查询逻辑
        return myDao.selectByPage(queryParam);
    }
}
```

### 2. 实现参数转换器

```java
public class MyParameterConverter implements ParameterConverter<MyQueryParam> {
    
    @Override
    public MyQueryParam convertForCount(AggregationPagingRequest request) {
        MyQueryParam param = new MyQueryParam();
        param.setKeyword(request.getKeyword());
        param.setCategoryId(request.getCategoryId());
        return param;
    }
    
    @Override
    public MyQueryParam convertForPage(AggregationPagingRequest request) {
        MyQueryParam param = convertForCount(request);
        param.setOffset(request.getOffset());
        param.setLimit(request.getLimit());
        return param;
    }
}
```

## 配置说明

### 1. 自动策略注册
框架会自动扫描并注册所有实现了`AggregationStrategy`接口的Bean。

### 2. 场景配置
策略会根据命名规则自动分配到相应场景：
- 包含"Hawk"的策略 → hawk_query场景
- 包含"General"的策略 → general_query场景
- 包含"Cdp"的策略 → 多场景支持

### 3. 优先级配置
策略按优先级排序执行，数值越小优先级越高。

## 最佳实践

### 1. 策略设计原则
- 单一职责：每个策略只负责一个数据源
- 无状态：策略应该是无状态的，可以并发使用
- 异常处理：妥善处理异常，避免影响其他策略

### 2. 性能优化
- 合理设置分页大小，避免一次查询过多数据
- 利用数据库索引优化查询性能
- 考虑使用缓存减少重复查询

### 3. 监控和日志
- 记录查询耗时和结果统计
- 监控各策略的执行情况
- 设置合理的超时时间

## 扩展功能

### 1. 缓存支持
可以在策略级别实现查询缓存，提高查询性能。

### 2. 异步查询
对于耗时较长的查询，可以考虑实现异步查询机制。

### 3. 结果聚合
可以扩展结果聚合逻辑，支持更复杂的数据合并需求。

## 故障排查

### 1. 常见问题
- 策略未注册：检查策略是否正确实现接口并标注为Bean
- 分页计算错误：检查offset和limit参数是否正确
- 查询超时：检查数据库查询性能和网络连接

### 2. 调试技巧
- 启用DEBUG日志查看详细执行过程
- 使用单一策略查询定位问题
- 检查策略管理器的统计信息

## 总结

通用聚合分页查询框架提供了一套完整的跨数据源查询解决方案，通过策略模式实现了高度的可扩展性和灵活性。开发者可以轻松地添加新的数据源支持，同时享受统一的查询接口和强大的分页能力。
