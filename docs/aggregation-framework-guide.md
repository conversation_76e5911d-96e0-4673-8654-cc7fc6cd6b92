# 通用聚合分页查询框架使用指南

## 概述

通用聚合分页查询框架是一个基于策略模式的跨数据源聚合查询解决方案，旨在解决多数据源分页查询的复杂性，提供统一、高效、可扩展的查询能力。

## 核心特性

### 1. 跨数据源聚合
- 支持多个数据源的数据聚合
- 统一的分页计算算法
- 自动处理数据源间的偏移量计算

### 2. 策略模式设计
- 每个数据源对应一个查询策略
- 策略可独立开发和测试
- 支持动态策略注册和管理

### 3. 灵活的查询条件
- 统一的查询请求模型
- 支持关键词、分类、状态等多种过滤条件
- 可扩展的参数体系

### 4. 高性能优化
- 智能的数据范围计算
- 避免不必要的数据库查询
- 支持查询缓存（可选）

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    AggregationPagingService                 │
│                        (服务门面)                            │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                AggregationPagingEngine                      │
│                   (聚合引擎)                                 │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              AggregationStrategyManager                     │
│                  (策略管理器)                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼──────┐ ┌────▼────┐ ┌──────▼──────┐
│   Strategy1  │ │Strategy2│ │  Strategy3  │
│   (索骥)     │ │ (CDP)   │ │  (通用)     │
└──────────────┘ └─────────┘ └─────────────┘
```

## 核心组件

### 1. AggregationPagingRequest
统一的查询请求模型，包含所有查询条件和分页参数。

```java
AggregationPagingRequest request = new AggregationPagingRequest()
    .setSceneName("feature_query")
    .setKeyword("用户")
    .setOffset(0)
    .setLimit(20)
    .setOrderBy("create_time")
    .addExtParam("onlyOnline", true);
```

### 2. AggregationStrategy
策略接口，定义了每个数据源需要实现的方法。

```java
public interface AggregationStrategy<T> {
    String getStrategyName();
    String getDataSource();
    int getPriority();
    boolean supports(AggregationPagingRequest request);
    long count(AggregationPagingRequest request);
    List<T> queryByPage(AggregationPagingRequest request);
}
```

### 3. AggregationPagingEngine
聚合查询引擎，负责执行跨数据源的分页查询逻辑。

### 4. AggregationStrategyManager
策略管理器，负责策略的注册、发现和选择。

## 使用方法

### 1. 基本查询

```java
@Autowired
private AggregationPagingService aggregationService;

public void basicQuery() {
    AggregationPagingRequest request = new AggregationPagingRequest()
        .setSceneName("general_query")
        .setKeyword("特征")
        .setOffset(0)
        .setLimit(20);
    
    AggregationPagingResponse<AggregationQueryResultDTO> response = 
        aggregationService.query(request);
    
    System.out.println("总数: " + response.getTotal());
    System.out.println("当前页数量: " + response.getSize());
}
```

### 2. 指定策略查询

```java
public void specificStrategyQuery() {
    AggregationPagingRequest request = new AggregationPagingRequest()
        .setKeyword("标签")
        .setOffset(0)
        .setLimit(10);
    
    List<String> strategies = Arrays.asList("CdpAggregateQueryStrategyImpl");
    AggregationPagingResponse<AggregationQueryResultDTO> response = 
        aggregationService.queryWithStrategies(request, strategies);
}
```

### 3. 单一数据源查询

```java
public void singleDataSourceQuery() {
    AggregationPagingRequest request = new AggregationPagingRequest()
        .setSceneName("general_query")
        .setCategoryId(1001);
    
    AggregationPagingResponse<AggregationQueryResultDTO> response = 
        aggregationService.queryFromDataSource(request, "cdp_label");
}
```

## 实现自定义策略

### 1. 继承抽象策略类

```java
@Component
public class MyCustomStrategy extends AbstractAggregationStrategy<MyResultDTO, MyQueryParam> {
    
    public MyCustomStrategy() {
        super(new MyParameterConverter());
    }
    
    @Override
    public String getDataSource() {
        return "my_datasource";
    }
    
    @Override
    public int getPriority() {
        return 100;
    }
    
    @Override
    protected long doCount(MyQueryParam queryParam) {
        // 实现计数逻辑
        return myDao.count(queryParam);
    }
    
    @Override
    protected List<MyResultDTO> doQueryByPage(MyQueryParam queryParam) {
        // 实现分页查询逻辑
        return myDao.selectByPage(queryParam);
    }
}
```

### 2. 实现参数转换器

```java
public class MyParameterConverter implements ParameterConverter<MyQueryParam> {
    
    @Override
    public MyQueryParam convertForCount(AggregationPagingRequest request) {
        MyQueryParam param = new MyQueryParam();
        param.setKeyword(request.getKeyword());
        param.setCategoryId(request.getCategoryId());
        return param;
    }
    
    @Override
    public MyQueryParam convertForPage(AggregationPagingRequest request) {
        MyQueryParam param = convertForCount(request);
        param.setOffset(request.getOffset());
        param.setLimit(request.getLimit());
        return param;
    }
}
```

## 配置说明

### 1. 自动策略注册
框架会自动扫描并注册所有实现了`AggregationStrategy`接口的Bean。

### 2. 场景配置
策略会根据命名规则自动分配到相应场景：
- 包含"Hawk"的策略 → hawk_query场景
- 包含"General"的策略 → general_query场景
- 包含"Cdp"的策略 → 多场景支持

### 3. 优先级配置
策略按优先级排序执行，数值越小优先级越高。

## 最佳实践

### 1. 策略设计原则
- 单一职责：每个策略只负责一个数据源
- 无状态：策略应该是无状态的，可以并发使用
- 异常处理：妥善处理异常，避免影响其他策略

### 2. 性能优化
- 合理设置分页大小，避免一次查询过多数据
- 利用数据库索引优化查询性能
- 考虑使用缓存减少重复查询

### 3. 监控和日志
- 记录查询耗时和结果统计
- 监控各策略的执行情况
- 设置合理的超时时间

## 分页模式支持

### 问题背景
在实际应用中，不同的数据源可能支持不同的分页方式：
- **数据库系统** - 通常支持 `LIMIT offset, count` (offset + limit)
- **第三方API** - 通常使用 `page=1&size=20` (page + size)
- **搜索引擎** - 可能支持 `from + size` 或 `page + size`
- **老系统** - 可能只支持页码方式

### 分页模式类型

#### 1. OFFSET_LIMIT 模式
```java
// MySQL风格
SELECT * FROM table LIMIT 20, 10;  -- 跳过20条，取10条

// 策略实现
@Override
public PagingMode getPagingMode() {
    return PagingMode.OFFSET_LIMIT;
}
```

#### 2. PAGE_SIZE 模式
```java
// API风格
GET /api/features?page=3&size=10  // 第3页，每页10条

// 策略实现
@Override
public PagingMode getPagingMode() {
    return PagingMode.PAGE_SIZE;
}

@Override
protected List<T> doQueryByPage(QueryParam param) {
    // 从扩展参数获取页码信息
    Integer pageNo = request.getExtParam("pageNo");
    Integer pageSize = request.getExtParam("pageSize");

    return apiClient.getFeatures(pageNo, pageSize);
}
```

#### 3. HYBRID 模式
```java
// 同时支持两种模式
@Override
public PagingMode getPagingMode() {
    return PagingMode.HYBRID;
}
```

### 分页参数转换

框架提供了 `PagingParameterConverter` 工具类来处理参数转换：

```java
// offset+limit 转 page+size
PageSizeParam pageParam = PagingParameterConverter.convertToPageSize(20, 10);
// 结果: pageNo=3, pageSize=10

// page+size 转 offset+limit
OffsetLimitParam offsetParam = PagingParameterConverter.convertToOffsetLimit(3, 10);
// 结果: offset=20, limit=10
```

### 数据范围计算

对于PAGE_SIZE模式，由于无法精确控制偏移量，框架会：

1. **计算合适的页码** - 根据请求的offset和limit计算需要查询的页码
2. **查询整页数据** - 调用策略查询整页数据
3. **提取有效数据** - 从查询结果中提取实际需要的数据

```java
// 请求: offset=15, limit=10
// 计算: 需要查询第2页 (pageNo=2, pageSize=20)
// 查询: 获取第2页的20条数据 (offset=20~39)
// 提取: 从结果中取索引[15-20, 25-20) = [0, 5) 的数据，共5条
// 继续: 查询第3页获取剩余5条数据
```

### 实现PAGE_SIZE策略

```java
@Component
public class ApiAggregationStrategy extends AbstractAggregationStrategy<ResultDTO, ApiQueryParam> {

    @Override
    public PagingMode getPagingMode() {
        return PagingMode.PAGE_SIZE;
    }

    @Override
    protected List<ResultDTO> doQueryByPage(ApiQueryParam param) {
        // 页码信息由框架自动设置到扩展参数中
        Integer pageNo = getCurrentRequest().getExtParam("pageNo");
        Integer pageSize = getCurrentRequest().getExtParam("pageSize");

        // 调用第三方API
        ApiResponse response = apiClient.queryFeatures(pageNo, pageSize);
        return convertToResultDTO(response.getData());
    }
}
```

### 性能优化建议

1. **合理的页大小** - 避免页码模式下的数据浪费
2. **缓存策略** - 对于页码模式，可以缓存整页数据
3. **预取机制** - 预测下一页需求，提前获取数据
4. **批量查询** - 尽量减少API调用次数

## 扩展功能

### 1. 缓存支持
可以在策略级别实现查询缓存，提高查询性能。

### 2. 异步查询
对于耗时较长的查询，可以考虑实现异步查询机制。

### 3. 结果聚合
可以扩展结果聚合逻辑，支持更复杂的数据合并需求。

## 故障排查

### 1. 常见问题
- 策略未注册：检查策略是否正确实现接口并标注为Bean
- 分页计算错误：检查offset和limit参数是否正确
- 查询超时：检查数据库查询性能和网络连接

### 2. 调试技巧
- 启用DEBUG日志查看详细执行过程
- 使用单一策略查询定位问题
- 检查策略管理器的统计信息

## 总结

通用聚合分页查询框架提供了一套完整的跨数据源查询解决方案，通过策略模式实现了高度的可扩展性和灵活性。开发者可以轻松地添加新的数据源支持，同时享受统一的查询接口和强大的分页能力。
