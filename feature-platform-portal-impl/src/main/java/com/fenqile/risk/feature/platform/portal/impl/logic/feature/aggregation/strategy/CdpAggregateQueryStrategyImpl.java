package com.fenqile.risk.feature.platform.portal.impl.logic.feature.aggregation.strategy;

import com.alibaba.fastjson.JSONObject;
import com.fenqile.label.management.service.response.label.DTO.LabelBaseDTO;
import com.fenqile.label.management.service.response.label.DTO.LabelMetaDTO;
import com.fenqile.label.management.service.response.label.DTO.LabelMetaListDTO;
import com.fenqile.risk.feature.platform.portal.impl.constant.HippoConstant;
import com.fenqile.risk.feature.platform.portal.impl.infra.rpc.cdp.LabelMetaServiceProxy;
import com.fenqile.risk.feature.platform.portal.impl.enums.AggregateFeatureSourceEnum;
import com.fenqile.risk.feature.platform.portal.impl.logic.feature.aggregation.converter.impl.CdpFeatureQueryParamConverter;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.AggregationQueryResultDTO;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.infra.cdp.CdpQueryReqDTO;
import com.fenqile.risk.feature.platform.portal.impl.util.FeatureAggregateConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class CdpAggregateQueryStrategyImpl extends AbstractFeatureAggregateQueryStrategy<CdpQueryReqDTO, LabelMetaDTO> {

    private final LabelMetaServiceProxy labelMetaServiceProxy;

    public CdpAggregateQueryStrategyImpl(CdpFeatureQueryParamConverter parameterConverter,
                                         LabelMetaServiceProxy labelMetaServiceProxy) {
        super(parameterConverter);
        this.labelMetaServiceProxy = labelMetaServiceProxy;
    }

    /**
     * 特征来源
     *
     * @return 特征来源
     */
    @Override
    public int getFeatureSource() {
        return AggregateFeatureSourceEnum.CDP_LABEL.getCode();
    }

    /**
     * 特征优先级,不能随意更改，后续排序会使用到
     *
     * @return 特征优先级
     */
    @Override
    public int getPriority() {
        return AggregateFeatureSourceEnum.CDP_LABEL.getPriority();
    }

    /**
     * 获取分页模式 - CDP只支持页码模式
     *
     * @return 分页模式
     */
    @Override
    public PagingMode getPagingMode() {
        return PagingMode.PAGE_SIZE;
    }

    /**
     * 获取支持的场景列表
     *
     * @return 产品列表，默认支持所有产品
     */
    @Override
    public List<String> getSupportedScenes() {
        return super.getSupportedScenes();
    }

    /**
     * 执行计数查询
     *
     * @param queryParam 查询参数
     * @return 计数结果
     */
    @Override
    protected int doCount(CdpQueryReqDTO queryParam) {
        return labelMetaServiceProxy.getLabelMetaCount(CdpQueryReqDTO.convertToLabelListReq(queryParam));
    }

    /**
     * 执行分页查询
     *
     * @param queryParam 查询参数
     * @return 查询结果
     */
    @Override
    protected List<LabelMetaDTO> doQueryByPage(CdpQueryReqDTO queryParam) {
        LabelMetaListDTO list = labelMetaServiceProxy.getList(CdpQueryReqDTO.convertToLabelListReq(queryParam));
        return list.getLabelMetaList();
    }

    /**
     * 执行按名称列表查询
     *
     * @param queryParam 查询参数
     * @return 查询结果
     */
    @Override
    protected List<LabelMetaDTO> doQueryByNameList(CdpQueryReqDTO queryParam) {
        return Collections.emptyList();
    }

    public List<AggregationQueryResultDTO> postProcessResults(List<LabelMetaDTO> results,
                                                              String sceneName) {
        if (results == null || results.isEmpty()) {
            return new ArrayList<>();
        }

        List<AggregationQueryResultDTO> resultList = new ArrayList<>();
        for (LabelMetaDTO labelMetaDTO : results) {
            if (labelMetaDTO == null || labelMetaDTO.getLabelBaseDTO() == null) {
                log.error("LabelMetaDTO or LabelBaseDTO is null, labelMetaDTO: {}", JSONObject.toJSONString(labelMetaDTO));
                resultList.add(new AggregationQueryResultDTO());
                continue;
            }
            LabelBaseDTO labelBaseDTO = labelMetaDTO.getLabelBaseDTO();
            AggregationQueryResultDTO aggregationQueryResultDTO = new AggregationQueryResultDTO();
            aggregationQueryResultDTO.setFeatureId(labelBaseDTO.getLabelId());
            aggregationQueryResultDTO.setFeatureName(labelBaseDTO.getLabelName());
            aggregationQueryResultDTO.setFeatureDesc(labelBaseDTO.getLabelNameCh());

            Pair<String, String> valueTypeAndDefaultPair = FeatureAggregateConvertUtil
                    .convertCdpValueTypeAndDefault4SuoJiRule(labelBaseDTO.getValueType(), labelBaseDTO.getValueDefault());
            aggregationQueryResultDTO.setValueType(valueTypeAndDefaultPair.getKey());

            aggregationQueryResultDTO.setValueDefault(valueTypeAndDefaultPair.getValue());
            //  TODO
            aggregationQueryResultDTO.setFeatureSource(AggregateFeatureSourceEnum.CDP_LABEL.getCode());

            //  写入固定值
            aggregationQueryResultDTO.setUseRemark(FeatureAggregateConvertUtil.CDP_LABEL_DEFAULT_P999_INFO);


            //  写入CDP标签对应的索骥分类id
            if (HippoConstant.cdp_label_field_category_list != null &&
                    !HippoConstant.cdp_label_field_category_list.isEmpty()) {
                aggregationQueryResultDTO.setCategoryId(HippoConstant.cdp_label_field_category_list.get(0));
            }
            //  状态转换
            aggregationQueryResultDTO.setFeatureAggregateState(FeatureAggregateConvertUtil.convertCdpLabelStateToSuoJiFieldOnlineState(labelBaseDTO.getState()));

            aggregationQueryResultDTO.setCreator(labelBaseDTO.getCreator());
            aggregationQueryResultDTO.setOperator(labelBaseDTO.getModifier());
            aggregationQueryResultDTO.setCreateTime(labelBaseDTO.getCreateTime());
            aggregationQueryResultDTO.setModifyTime(labelBaseDTO.getModifyTime());

            //  CDP标签没有的字段，统计设置默认值
            aggregationQueryResultDTO.setIsXinFeature(false);
            aggregationQueryResultDTO.setIsSupportBacktrack(null);
            aggregationQueryResultDTO.setCreditProdCode(null);
            aggregationQueryResultDTO.setCreditProdSubCode(null);
            aggregationQueryResultDTO.setBacktrackDataFeatureName(null);
            aggregationQueryResultDTO.setFieldType(null);
            aggregationQueryResultDTO.setOutFieldName(null);
            aggregationQueryResultDTO.setMethodId(null);
            aggregationQueryResultDTO.setUsePurpose(null);
            aggregationQueryResultDTO.setAdapter(null);
            aggregationQueryResultDTO.setQueryType(null);
            resultList.add(aggregationQueryResultDTO);
        }
        return resultList;
    }
}
