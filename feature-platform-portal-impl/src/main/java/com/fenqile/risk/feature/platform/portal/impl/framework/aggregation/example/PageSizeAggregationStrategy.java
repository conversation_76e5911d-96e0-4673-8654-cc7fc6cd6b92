package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.example;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AbstractAggregationStrategy;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.AggregationQueryResultDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 支持页码模式的聚合策略示例
 * 
 * 展示如何实现只支持page+size分页模式的策略
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
public class PageSizeAggregationStrategy extends AbstractAggregationStrategy<AggregationQueryResultDTO, PageSizeAggregationStrategy.PageSizeQueryParam> {

    private final PageSizeFeatureDao featureDao;

    public PageSizeAggregationStrategy(PageSizeFeatureDao featureDao) {
        super(new PageSizeParameterConverter());
        this.featureDao = featureDao;
    }

    @Override
    public String getDataSource() {
        return "page_size_api";
    }

    @Override
    public int getPriority() {
        return 200;
    }

    @Override
    public PagingMode getPagingMode() {
        // 声明只支持页码模式
        return PagingMode.PAGE_SIZE;
    }

    @Override
    public boolean supports(AggregationPagingRequest request) {
        return "api_query".equals(request.getSceneName()) || "default".equals(request.getSceneName());
    }

    @Override
    protected long doCount(PageSizeQueryParam queryParam) {
        return featureDao.countByParam(queryParam);
    }

    @Override
    protected List<AggregationQueryResultDTO> doQueryByPage(PageSizeQueryParam queryParam) {
        // 使用页码进行查询
        List<PageSizeFeatureEntity> entities = featureDao.selectByPage(queryParam);
        return convertToResultDTO(entities);
    }

    /**
     * 转换实体为结果DTO
     */
    private List<AggregationQueryResultDTO> convertToResultDTO(List<PageSizeFeatureEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return Collections.emptyList();
        }

        List<AggregationQueryResultDTO> results = new ArrayList<>();
        for (PageSizeFeatureEntity entity : entities) {
            AggregationQueryResultDTO dto = new AggregationQueryResultDTO();
            dto.setFeatureName(entity.getName());
            dto.setFeatureDesc(entity.getDescription());
            dto.setFeatureSource(getDataSource());
            dto.setCreateTime(entity.getCreateTime());
            dto.setModifyTime(entity.getModifyTime());
            results.add(dto);
        }
        return results;
    }

    /**
     * 页码查询参数
     */
    @Data
    public static class PageSizeQueryParam {
        private String nameLike;
        private List<String> nameList;
        private Integer categoryId;
        private Integer status;
        private Integer pageNo;      // 页码，从1开始
        private Integer pageSize;    // 页大小
        private String orderBy;
    }

    /**
     * 页码参数转换器
     */
    public static class PageSizeParameterConverter implements ParameterConverter<PageSizeQueryParam> {

        @Override
        public PageSizeQueryParam convertForCount(AggregationPagingRequest request) {
            PageSizeQueryParam param = new PageSizeQueryParam();
            setCommonConditions(param, request);
            return param;
        }

        @Override
        public PageSizeQueryParam convertForPage(AggregationPagingRequest request) {
            PageSizeQueryParam param = new PageSizeQueryParam();
            setCommonConditions(param, request);
            
            // 从扩展参数中获取页码信息（由引擎设置）
            Integer pageNo = request.getExtParam("pageNo");
            Integer pageSize = request.getExtParam("pageSize");
            
            if (pageNo != null && pageSize != null) {
                param.setPageNo(pageNo);
                param.setPageSize(pageSize);
            } else {
                // 如果没有页码信息，尝试从offset+limit转换
                if (request.getLimit() != null && request.getLimit() > 0) {
                    int offset = request.getOffset() != null ? request.getOffset() : 0;
                    param.setPageNo((offset / request.getLimit()) + 1);
                    param.setPageSize(request.getLimit());
                } else {
                    // 默认值
                    param.setPageNo(1);
                    param.setPageSize(20);
                }
            }
            
            // 设置排序
            if (StringUtils.hasText(request.getOrderBy())) {
                param.setOrderBy(request.getOrderBy() + " " + request.getOrderDirection());
            } else {
                param.setOrderBy("create_time DESC");
            }
            
            log.debug("页码参数转换: pageNo={}, pageSize={}", param.getPageNo(), param.getPageSize());
            
            return param;
        }

        private void setCommonConditions(PageSizeQueryParam param, AggregationPagingRequest request) {
            param.setNameLike(request.getKeyword());
            param.setNameList(request.getNameList());
            param.setCategoryId(request.getCategoryId());
            param.setStatus(request.getStatus());
        }
    }

    /**
     * 页码特征实体
     */
    @Data
    public static class PageSizeFeatureEntity {
        private String id;
        private String name;
        private String description;
        private Integer categoryId;
        private Integer status;
        private Long createTime;
        private Long modifyTime;
    }

    /**
     * 页码DAO接口
     * 
     * 模拟第三方API或只支持页码分页的系统
     */
    public interface PageSizeFeatureDao {
        
        /**
         * 计数查询
         */
        long countByParam(PageSizeQueryParam param);
        
        /**
         * 页码分页查询
         * 
         * @param param 查询参数，包含pageNo和pageSize
         * @return 查询结果
         */
        List<PageSizeFeatureEntity> selectByPage(PageSizeQueryParam param);
    }

    /**
     * 模拟DAO实现
     */
    public static class MockPageSizeFeatureDao implements PageSizeFeatureDao {
        
        @Override
        public long countByParam(PageSizeQueryParam param) {
            // 模拟计数查询
            log.info("模拟页码计数查询: {}", param);
            return 100; // 假设总共100条数据
        }
        
        @Override
        public List<PageSizeFeatureEntity> selectByPage(PageSizeQueryParam param) {
            // 模拟分页查询
            log.info("模拟页码分页查询: pageNo={}, pageSize={}", param.getPageNo(), param.getPageSize());
            
            List<PageSizeFeatureEntity> results = new ArrayList<>();
            
            // 模拟生成数据
            int startIndex = (param.getPageNo() - 1) * param.getPageSize();
            for (int i = 0; i < param.getPageSize(); i++) {
                int index = startIndex + i;
                if (index >= 100) break; // 假设总共100条数据
                
                PageSizeFeatureEntity entity = new PageSizeFeatureEntity();
                entity.setId("api_feature_" + index);
                entity.setName("API特征_" + index);
                entity.setDescription("来自API的特征数据_" + index);
                entity.setCategoryId(1000);
                entity.setStatus(1);
                entity.setCreateTime(System.currentTimeMillis());
                entity.setModifyTime(System.currentTimeMillis());
                
                results.add(entity);
            }
            
            log.info("模拟页码查询返回{}条数据", results.size());
            return results;
        }
    }
}
