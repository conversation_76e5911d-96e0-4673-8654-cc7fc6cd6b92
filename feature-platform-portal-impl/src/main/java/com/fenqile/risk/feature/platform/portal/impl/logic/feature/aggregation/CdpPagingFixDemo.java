package com.fenqile.risk.feature.platform.portal.impl.logic.feature.aggregation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * CDP分页问题修复演示
 * 
 * 展示修复前后的差异和解决方案
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Component
public class CdpPagingFixDemo {

    /**
     * 演示CDP分页问题和解决方案
     */
    public void demonstrateCdpPagingFix() {
        log.info("=== CDP分页问题修复演示 ===");
        
        // 问题场景：请求offset=15, limit=10
        int requestOffset = 15;
        int requestLimit = 10;
        
        log.info("请求场景: offset={}, limit={} (需要第15-24条数据)", requestOffset, requestLimit);
        
        // 展示修复前的问题
        demonstrateOldProblem(requestOffset, requestLimit);
        
        // 展示修复后的解决方案
        demonstrateNewSolution(requestOffset, requestLimit);
        
        // 展示其他测试场景
        demonstrateOtherScenarios();
    }

    /**
     * 演示修复前的问题
     */
    private void demonstrateOldProblem(int offset, int limit) {
        log.info("--- 修复前的问题 ---");
        
        // 旧逻辑：简单的offset/limit转换
        int pageSize = limit;  // 直接用limit作为pageSize
        int pageNo = (offset / limit) + 1;  // 简单除法
        
        log.info("旧转换逻辑:");
        log.info("  pageSize = limit = {}", pageSize);
        log.info("  pageNo = (offset / limit) + 1 = ({} / {}) + 1 = {}", offset, limit, pageNo);
        
        // 计算实际查询范围
        int actualStartOffset = (pageNo - 1) * pageSize;
        int actualEndOffset = actualStartOffset + pageSize;
        
        log.info("实际查询:");
        log.info("  查询第{}页，每页{}条", pageNo, pageSize);
        log.info("  实际获取数据范围: [{}, {})", actualStartOffset, actualEndOffset);
        
        // 分析问题
        int requestEndOffset = offset + limit;
        log.info("需求数据范围: [{}, {})", offset, requestEndOffset);
        
        int coverStart = Math.max(offset, actualStartOffset);
        int coverEnd = Math.min(requestEndOffset, actualEndOffset);
        int coveredCount = Math.max(0, coverEnd - coverStart);
        int missedCount = limit - coveredCount;
        
        if (missedCount > 0) {
            log.error("❌ 问题: 丢失了{}条数据! (第{}-{}条)", missedCount, coverEnd, requestEndOffset - 1);
        } else {
            log.info("✅ 数据完整");
        }
    }

    /**
     * 演示修复后的解决方案
     */
    private void demonstrateNewSolution(int offset, int limit) {
        log.info("--- 修复后的解决方案 ---");
        
        log.info("新逻辑: 多次查询 + 数据提取");
        
        int remaining = limit;
        int currentOffset = offset;
        int queryCount = 0;
        int totalFetched = 0;
        
        while (remaining > 0) {
            queryCount++;
            
            // 计算最优页大小
            int pageSize = calculateOptimalPageSize(remaining);
            int pageNo = (currentOffset / pageSize) + 1;
            
            log.info("第{}次查询:", queryCount);
            log.info("  pageNo={}, pageSize={}", pageNo, pageSize);
            
            // 计算页面数据范围
            int pageStartOffset = (pageNo - 1) * pageSize;
            int pageEndOffset = pageStartOffset + pageSize;
            
            log.info("  页面数据范围: [{}, {})", pageStartOffset, pageEndOffset);
            
            // 计算有效数据范围
            int validStartIndex = Math.max(0, currentOffset - pageStartOffset);
            int validEndIndex = Math.min(pageSize, validStartIndex + remaining);
            int validCount = Math.max(0, validEndIndex - validStartIndex);
            
            log.info("  有效数据: 索引[{}, {}), 获取{}条", validStartIndex, validEndIndex, validCount);
            
            totalFetched += validCount;
            remaining -= validCount;
            currentOffset += validCount;
            
            // 模拟数据不足的情况
            if (pageStartOffset + pageSize > 200) { // 假设总共200条数据
                log.info("  到达数据末尾");
                break;
            }
        }
        
        log.info("✅ 查询完成: 总共{}次查询, 获取{}条数据", queryCount, totalFetched);
        
        if (totalFetched >= limit) {
            log.info("✅ 数据完整: 成功获取所需的{}条数据", limit);
        } else {
            log.warn("⚠️ 数据不足: 请求{}条, 实际获取{}条", limit, totalFetched);
        }
    }

    /**
     * 演示其他测试场景
     */
    private void demonstrateOtherScenarios() {
        log.info("--- 其他测试场景 ---");
        
        int[][] testCases = {
                {0, 20},    // 对齐情况
                {25, 30},   // 跨页情况  
                {50, 5},    // 小批量
                {100, 1}    // 单条数据
        };
        
        for (int[] testCase : testCases) {
            int offset = testCase[0];
            int limit = testCase[1];
            
            log.info("测试场景: offset={}, limit={}", offset, limit);
            
            // 分析查询效率
            int queryCount = calculateQueryCount(offset, limit);
            double efficiency = calculateEfficiency(offset, limit);
            
            log.info("  预计查询次数: {}", queryCount);
            log.info("  数据传输效率: {:.1f}%", efficiency * 100);
        }
    }

    /**
     * 计算最优页大小
     */
    private int calculateOptimalPageSize(int remaining) {
        if (remaining <= 10) {
            return Math.max(20, remaining * 2);
        } else if (remaining <= 50) {
            return Math.max(50, remaining);
        } else {
            return Math.min(100, remaining);
        }
    }

    /**
     * 计算查询次数
     */
    private int calculateQueryCount(int offset, int limit) {
        int remaining = limit;
        int currentOffset = offset;
        int queryCount = 0;
        
        while (remaining > 0) {
            queryCount++;
            int pageSize = calculateOptimalPageSize(remaining);
            int pageStartOffset = ((currentOffset / pageSize)) * pageSize;
            int validCount = Math.min(pageSize - (currentOffset - pageStartOffset), remaining);
            
            remaining -= validCount;
            currentOffset += validCount;
            
            if (validCount <= 0) break;
        }
        
        return queryCount;
    }

    /**
     * 计算数据传输效率
     */
    private double calculateEfficiency(int offset, int limit) {
        int totalTransferred = 0;
        int remaining = limit;
        int currentOffset = offset;
        
        while (remaining > 0) {
            int pageSize = calculateOptimalPageSize(remaining);
            totalTransferred += pageSize;
            
            int pageStartOffset = ((currentOffset / pageSize)) * pageSize;
            int validCount = Math.min(pageSize - (currentOffset - pageStartOffset), remaining);
            
            remaining -= validCount;
            currentOffset += validCount;
            
            if (validCount <= 0) break;
        }
        
        return totalTransferred > 0 ? (double) limit / totalTransferred : 0;
    }

    /**
     * 展示关键改造点
     */
    public void showKeyChanges() {
        log.info("=== 关键改造点总结 ===");
        
        log.info("1. 策略层改造:");
        log.info("   - CdpAggregateQueryStrategyImpl 实现 PagingModeAware 接口");
        log.info("   - 声明分页模式为 PAGE_SIZE");
        
        log.info("2. 服务层改造:");
        log.info("   - FeatureAggregateQueryServiceLogic 增加分页模式感知");
        log.info("   - 新增 fetchDataFromPageSizeStrategy 方法支持多次查询");
        
        log.info("3. 参数转换改造:");
        log.info("   - CdpFeatureQueryParamConverter 支持扩展参数");
        log.info("   - BaseAggregationQueryReqDTO 实现 ExtendableRequest 接口");
        
        log.info("4. 核心算法:");
        log.info("   - 智能页大小计算");
        log.info("   - 多次查询数据合并");
        log.info("   - 精确数据范围提取");
    }

    /**
     * 运行完整演示
     */
    public void runFullDemo() {
        try {
            demonstrateCdpPagingFix();
            showKeyChanges();
            
            log.info("=== CDP分页问题修复演示完成 ===");
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        }
    }
}
