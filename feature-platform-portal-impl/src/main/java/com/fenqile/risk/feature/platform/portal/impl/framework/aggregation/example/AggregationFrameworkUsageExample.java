package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.example;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.manager.AggregationStrategyManager;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingResponse;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.service.AggregationPagingService;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.AggregationQueryResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 聚合框架使用示例
 * 
 * 展示如何使用通用聚合分页查询框架
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Component
public class AggregationFrameworkUsageExample {

    private final AggregationPagingService aggregationService;
    private final AggregationStrategyManager strategyManager;

    public AggregationFrameworkUsageExample(AggregationPagingService aggregationService,
                                          AggregationStrategyManager strategyManager) {
        this.aggregationService = aggregationService;
        this.strategyManager = strategyManager;
    }

    /**
     * 示例1: 基本聚合查询
     */
    public void basicAggregationQuery() {
        log.info("=== 示例1: 基本聚合查询 ===");

        // 构建查询请求
        AggregationPagingRequest request = new AggregationPagingRequest()
                .setSceneName("feature_query")
                .setKeyword("用户")
                .setOffset(0)
                .setLimit(20)
                .setOrderBy("create_time")
                .setOrderDirection("DESC");

        // 执行查询
        AggregationPagingResponse<AggregationQueryResultDTO> response = aggregationService.query(request);

        // 处理结果
        log.info("查询结果: 总数={}, 当前页数量={}, 耗时={}ms", 
                response.getTotal(), response.getSize(), response.getCostTime());
        
        for (AggregationQueryResultDTO item : response.getData()) {
            log.info("特征: {} - {}", item.getFeatureName(), item.getFeatureDesc());
        }
    }

    /**
     * 示例2: 指定策略查询
     */
    public void specificStrategyQuery() {
        log.info("=== 示例2: 指定策略查询 ===");

        AggregationPagingRequest request = new AggregationPagingRequest()
                .setKeyword("标签")
                .setOffset(0)
                .setLimit(10);

        // 指定使用特定策略
        List<String> strategyNames = Arrays.asList("SuoJiAggregateQueryStrategyImpl", "CdpAggregateQueryStrategyImpl");
        AggregationPagingResponse<AggregationQueryResultDTO> response = 
                aggregationService.queryWithStrategies(request, strategyNames);

        log.info("指定策略查询结果: 总数={}, 耗时={}ms", response.getTotal(), response.getCostTime());
    }

    /**
     * 示例3: 单一数据源查询
     */
    public void singleDataSourceQuery() {
        log.info("=== 示例3: 单一数据源查询 ===");

        AggregationPagingRequest request = new AggregationPagingRequest()
                .setSceneName("general_query")
                .setCategoryId(1001)
                .setOffset(0)
                .setLimit(15);

        // 只从CDP数据源查询
        AggregationPagingResponse<AggregationQueryResultDTO> response = 
                aggregationService.queryFromDataSource(request, "cdp_label");

        log.info("单一数据源查询结果: 总数={}, 耗时={}ms", response.getTotal(), response.getCostTime());
    }

    /**
     * 示例4: 复杂条件查询
     */
    public void complexConditionQuery() {
        log.info("=== 示例4: 复杂条件查询 ===");

        AggregationPagingRequest request = new AggregationPagingRequest()
                .setSceneName("hawk_query")
                .setKeyword("风险")
                .setNameList(Arrays.asList("user_age", "user_income", "risk_score"))
                .setCategoryId(2001)
                .setStatus(1)
                .setStartTime(System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L) // 30天前
                .setEndTime(System.currentTimeMillis())
                .setOffset(0)
                .setLimit(50)
                .addExtParam("onlyOnlineFeature", true)
                .addExtParam("excludeMigrated", true);

        AggregationPagingResponse<AggregationQueryResultDTO> response = aggregationService.query(request);

        log.info("复杂条件查询结果: 总数={}, 有更多数据={}, 耗时={}ms", 
                response.getTotal(), response.isHasMore(), response.getCostTime());
    }

    /**
     * 示例5: 分页遍历所有数据
     */
    public void paginationTraversal() {
        log.info("=== 示例5: 分页遍历所有数据 ===");

        int pageSize = 20;
        int offset = 0;
        int totalProcessed = 0;

        do {
            AggregationPagingRequest request = new AggregationPagingRequest()
                    .setSceneName("general_query")
                    .setKeyword("特征")
                    .setOffset(offset)
                    .setLimit(pageSize);

            AggregationPagingResponse<AggregationQueryResultDTO> response = aggregationService.query(request);

            if (response.isEmpty()) {
                break;
            }

            // 处理当前页数据
            for (AggregationQueryResultDTO item : response.getData()) {
                // 处理每个特征
                totalProcessed++;
            }

            log.info("处理第{}页, 当前页数量={}, 累计处理={}", 
                    (offset / pageSize) + 1, response.getSize(), totalProcessed);

            offset += pageSize;

            // 如果没有更多数据，退出循环
            if (!response.isHasMore()) {
                break;
            }

        } while (true);

        log.info("分页遍历完成, 总共处理={}条数据", totalProcessed);
    }

    /**
     * 示例6: 策略管理和监控
     */
    public void strategyManagement() {
        log.info("=== 示例6: 策略管理和监控 ===");

        // 获取所有策略统计
        AggregationStrategyManager.StrategyStatistics stats = strategyManager.getStatistics();
        log.info("策略统计: {}", stats);

        // 获取所有支持的场景
        log.info("支持的场景: {}", strategyManager.getAllScenes());

        // 获取所有支持的数据源
        log.info("支持的数据源: {}", strategyManager.getAllDataSources());

        // 获取特定场景的策略
        List<?> strategies = strategyManager.getStrategies("general_query");
        log.info("general_query场景的策略数量: {}", strategies.size());
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        try {
            basicAggregationQuery();
            specificStrategyQuery();
            singleDataSourceQuery();
            complexConditionQuery();
            paginationTraversal();
            strategyManagement();
        } catch (Exception e) {
            log.error("运行示例时发生错误", e);
        }
    }
}
