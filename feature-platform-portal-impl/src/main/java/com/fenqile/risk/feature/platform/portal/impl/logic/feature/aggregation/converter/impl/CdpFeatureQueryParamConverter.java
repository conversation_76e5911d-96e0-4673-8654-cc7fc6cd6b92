package com.fenqile.risk.feature.platform.portal.impl.logic.feature.aggregation.converter.impl;

import com.fenqile.label.management.common.enums.ComputationTimelinessEnum;
import com.fenqile.label.management.common.enums.LabelStateEnum;
import com.fenqile.label.management.common.enums.UsedSceneListEnum;
import com.fenqile.risk.feature.platform.portal.impl.constant.HippoConstant;
import com.fenqile.risk.feature.platform.portal.impl.logic.feature.aggregation.converter.BaseQueryParameterConverter;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.req.BaseAggregationQueryReqDTO;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.req.GeneralAggregationQueryDTO;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.req.HawkAggregationQueryDTO;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.infra.cdp.CdpQueryReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class CdpFeatureQueryParamConverter extends BaseQueryParameterConverter<CdpQueryReqDTO> {

    /**
     * 标签实时在线查询-已写入缓存
     * ps: 目前没有其他值，先在这里定义一个
     */
    private static final Integer REALTIME_ONLINE_QUERY = 1;

    /**
     * 调用CDP接口获取标签查询的标签类型，目前只查询:20,30这两种类型
     */
    public static final List<Integer> QUERY_LABEL_COMPUTATION_TIMELINESS_LIST =
            Arrays.asList(ComputationTimelinessEnum.PRE_D.getCode(), ComputationTimelinessEnum.FLINK.getCode());

    /**
     * 创建基础查询参数对象
     *
     * @return 基础查询参数
     */
    @Override
    protected CdpQueryReqDTO createBaseQueryParam() {
        return new CdpQueryReqDTO();
    }

    /**
     * 设置基本查询条件
     *
     * @param queryParam 查询参数
     * @param queryDTO   通用查询DTO
     */
    @Override
    protected void setBasicQueryConditions(CdpQueryReqDTO queryParam, BaseAggregationQueryReqDTO queryDTO) {
        if (StringUtils.isNotBlank(queryDTO.getFeatureNameLike())) {
            // 设置特征名称模糊查询条件
            queryParam.setLabelName(queryDTO.getFeatureNameLike());
        }
        if (queryDTO.getFeatureNameList() != null && !queryDTO.getFeatureNameList().isEmpty()) {
            // 设置特征名称列表查询条件
            queryParam.setLabelNameList(queryDTO.getFeatureNameList());
        }

        queryParam.setIsNeedEnum(false);
        queryParam.setIsNeedVersionInfo(true);

        // 使用了computationTimelinessList这个参数，那么就必须把isNeedSuoJiField设置为false，否则会报错
        queryParam.setIsNeedSuoJiField(false);
        queryParam.setComputationTimelinessList(QUERY_LABEL_COMPUTATION_TIMELINESS_LIST);

        // 字段名和标签重名的字段、迁移中未验证通过的字段，离线标签不展示这些字段，需要过滤
        List<String> excludeLabelNameList = new ArrayList<>();
        excludeLabelNameList.addAll(new ArrayList<>(HippoConstant.SJ_DUPLICATION_NAME_FIELDS));
        excludeLabelNameList.addAll(new ArrayList<>(HippoConstant.MIGRATING_UN_VERIFIED_FIELD_NAMES));
        if (CollectionUtils.isNotEmpty(excludeLabelNameList)) {
            queryParam.setExcludeLabelNameList(excludeLabelNameList);
        }
    }

    @Override
    protected void setLimitOffsetConditions(CdpQueryReqDTO queryParam, BaseAggregationQueryReqDTO queryDTO) {
        // CDP只支持页码模式，从扩展参数中获取页码信息（由聚合引擎设置）
        Integer pageNo = queryDTO.getExtParam("pageNo");
        Integer pageSize = queryDTO.getExtParam("pageSize");

        if (pageNo != null && pageSize != null) {
            queryParam.setPageNo(pageNo);
            queryParam.setPageSize(pageSize);
        } else {
            // 兼容旧逻辑：如果没有页码信息，尝试从offset+limit转换
            // 注意：这种转换可能导致数据丢失，建议使用新的聚合引擎
            Integer limit = queryDTO.getLimit();
            Integer offset = queryDTO.getOffset();

            if (limit != null && limit > 0) {
                queryParam.setPageSize(limit);

                if (offset != null && offset >= 0) {
                    queryParam.setPageNo(offset / limit + 1);
                }
            }
        }
    }

    @Override
    protected void applyHawkSceneSpecificConfig(CdpQueryReqDTO queryParam, HawkAggregationQueryDTO queryDTO) {
        queryParam.setUsedScene(UsedSceneListEnum.REALTIME_ONLINE_QUERY.getCode());
        //  米霍克只查询上线的CDP标签
        queryParam.setState(LabelStateEnum.ONLINE.getCode());
    }

    @Override
    protected void applyGeneralSceneSpecificConfig(CdpQueryReqDTO queryParam, GeneralAggregationQueryDTO queryDTO) {
        // 如果没有指定查询全部状态，那么设置标签状态查询条件，这里做判断是为了查询标签
        if (queryDTO.isOnlyQueryOnlineFeature()) {
            queryParam.setState(LabelStateEnum.ONLINE.getCode());
        }
        if (REALTIME_ONLINE_QUERY.equals(queryDTO.getUsedScene())) {
            // 如果是实时在线查询场景，设置查询场景为实时在线查询
            queryParam.setUsedScene(UsedSceneListEnum.REALTIME_ONLINE_QUERY.getCode());
        }
    }

    /**
     * 获取支持的特征来源
     *
     * @return 特征来源代码
     */
    @Override
    public Integer getSupportedSource() {
        return 0;
    }

    /**
     * 获取转换器名称
     *
     * @return 转换器名称
     */
    @Override
    public String getConverterName() {
        return "CdpFeatureQueryParamConverter";
    }
}
