package com.fenqile.risk.feature.platform.portal.impl.logic.feature.aggregation;

import cn.hutool.core.collection.CollectionUtil;
import com.fenqile.risk.feature.platform.portal.impl.constant.HippoConstant;
import com.fenqile.risk.feature.platform.portal.impl.enums.AggregateFeatureSourceEnum;
import com.fenqile.risk.feature.platform.portal.impl.enums.AggregateQuerySceneEnum;
import com.fenqile.risk.feature.platform.portal.impl.exception.BizLogicException;
import com.fenqile.risk.feature.platform.portal.impl.exception.FeaturePortalExceptionCode;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.AggregationQueryResultDTO;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.req.BaseAggregationQueryReqDTO;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.req.GeneralAggregationQueryDTO;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.req.HawkAggregationQueryDTO;
import com.fenqile.risk.feature.platform.portal.impl.util.FeaturePageUtil;
import com.fenqile.risk.feature.platform.portal.request.feature.aggregation.FeatureAggregateQuery4GeneralReq;
import com.fenqile.risk.feature.platform.portal.request.feature.aggregation.FeatureAggregateQuery4HawkReq;
import com.fenqile.risk.feature.platform.portal.response.PageInfoVO;
import com.fenqile.risk.feature.platform.portal.response.feature.aggregation.FeatureAggregateQuery4GeneralResp;
import com.fenqile.risk.feature.platform.portal.response.feature.aggregation.FeatureAggregateQuery4HawkResp;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class FeatureAggregateQueryServiceLogic {

    private final FeatureAggregateQueryStrategyContext featureAggregateQueryStrategyContext;

    public FeatureAggregateQueryServiceLogic(FeatureAggregateQueryStrategyContext featureAggregateQueryStrategyContext) {
        this.featureAggregateQueryStrategyContext = featureAggregateQueryStrategyContext;
    }


    /**
     * 米霍克场景特征聚合查询
     *
     * @param req 米霍克查询请求
     * @return 分页查询结果
     */
    public PageInfoVO<FeatureAggregateQuery4HawkResp> pageQuery4Hawk(FeatureAggregateQuery4HawkReq req) {
        //  入参转换
        HawkAggregationQueryDTO hawkAggregationQueryDTO = HawkAggregationQueryDTO.getHawkAggregationQueryDTO(req);

        //  获取查询策略
        List<FeatureAggregateQueryStrategy> strategies = determineHawkQueryStrategies(req);


        //  查询
        PageInfoVO<AggregationQueryResultDTO> aggregationQueryResultDTOPageInfoVO;
        try {
            aggregationQueryResultDTOPageInfoVO = aggregateQueryPageBySource(hawkAggregationQueryDTO, strategies);
        } catch (Exception e) {
            log.error("Hawk aggregation query failed, req: {}", req, e);
            throw new BizLogicException(FeaturePortalExceptionCode.ERROR_SYS);
        }

        //  结果转换
        List<FeatureAggregateQuery4HawkResp> respList = new ArrayList<>();
        for (AggregationQueryResultDTO aggregationQueryResultDTO : aggregationQueryResultDTOPageInfoVO.getResultRows()) {
            respList.add(AggregationQueryResultDTO.getFeatureAggregateQuery4HawkResp(aggregationQueryResultDTO));
        }
        return FeaturePageUtil.convertPageToPageInfo(aggregationQueryResultDTOPageInfoVO.getTotal(), respList);
    }

    /**
     * 获取米霍克查询场景的查询策略
     *
     * @param req req
     * @return List<FeatureAggregateQueryStrategy>
     */
    private List<FeatureAggregateQueryStrategy> determineHawkQueryStrategies(FeatureAggregateQuery4HawkReq req) {
        List<Integer> queryFeatureSources = new ArrayList<>(AggregateFeatureSourceEnum.SOURCE_LIST);
        Integer categoryId = req.getCategoryId();
        if (categoryId != null) {
            // 如果分类指定了是CDP标签的分类，那么就只查询CDP就好了
            if (CollectionUtil.contains(HippoConstant.cdp_label_field_category_list, categoryId)) {
                queryFeatureSources.removeIf(source -> !source.equals(AggregateFeatureSourceEnum.CDP_LABEL.getCode()));
            } else {
                //  分类不为空，也不是CDP，特征没有分类id，那么这里就只需要查询索骥就好了
                queryFeatureSources.removeIf(source -> !source.equals(AggregateFeatureSourceEnum.SUOJI_FIELD.getCode()));
            }
        }

        Integer outFieldNameNotEmpty = req.getOutFieldNameNotEmpty();
        if (outFieldNameNotEmpty != null && outFieldNameNotEmpty == 1) {
            //  如果 outFieldNameNotEmpty 为 1，则只查询索骥字段
            queryFeatureSources.removeIf(source -> !source.equals(AggregateFeatureSourceEnum.SUOJI_FIELD.getCode()));
        }

        if (queryFeatureSources.isEmpty()) {
            // 如果没有查询源了，那么就直接返回空
            log.warn("No valid feature sources found for the given req: {}", req);
            return Collections.emptyList();
        }
        // 根据查询源过滤策略
        List<FeatureAggregateQueryStrategy> strategies = featureAggregateQueryStrategyContext.getStrategies(AggregateQuerySceneEnum.HAWK_QUERY.getCode());

        strategies.removeIf(strategy -> !queryFeatureSources.contains(strategy.getFeatureSource()));

        return strategies;
    }

    /**
     * 米霍克场景特征聚合查询
     *
     * @param req 米霍克查询请求
     * @return 分页查询结果
     */
    public PageInfoVO<FeatureAggregateQuery4GeneralResp> page(FeatureAggregateQuery4GeneralReq req) {
        //  入参转换
        GeneralAggregationQueryDTO generalAggregationQueryDTO = GeneralAggregationQueryDTO.getGeneralAggregationQueryDTO(req);
        // 获取查询策略
        List<FeatureAggregateQueryStrategy> strategies = getFeatureAggregateQueryStrategies(req);
        // 查询
        PageInfoVO<AggregationQueryResultDTO> aggregationQueryResultDTOPageInfoVO;
        try {
            aggregationQueryResultDTOPageInfoVO = aggregateQueryPageBySource(generalAggregationQueryDTO, strategies);
        } catch (Exception e) {
            log.error("general aggregation query failed, req: {}", req, e);
            throw new BizLogicException(FeaturePortalExceptionCode.ERROR_SYS);
        }

        // 结果转换
        List<FeatureAggregateQuery4GeneralResp> respList = new ArrayList<>();
        for (AggregationQueryResultDTO aggregationQueryResultDTO : aggregationQueryResultDTOPageInfoVO.getResultRows()) {
            respList.add(AggregationQueryResultDTO.getFeatureAggregateQuery4GeneralResp(aggregationQueryResultDTO));
        }
        return FeaturePageUtil.convertPageToPageInfo(aggregationQueryResultDTOPageInfoVO.getTotal(), respList);
    }

    /**
     * 确定通用查询使用的策略
     *
     * @param req 请求参数
     * @return List<FeatureAggregateQueryStrategy>
     */
    private List<FeatureAggregateQueryStrategy> getFeatureAggregateQueryStrategies(FeatureAggregateQuery4GeneralReq req) {
        List<FeatureAggregateQueryStrategy> strategies = featureAggregateQueryStrategyContext.getStrategies(AggregateQuerySceneEnum.GENERAL_QUERY.getCode());

        Integer categoryId = req.getCategoryId();
        if (categoryId != null) {
            // 如果分类指定了是CDP标签的分类，那么就只查询CDP就好了
            if (CollectionUtil.contains(HippoConstant.cdp_label_field_category_list, categoryId)) {
                strategies = Collections.singletonList(featureAggregateQueryStrategyContext.getStrategy(AggregateQuerySceneEnum.GENERAL_QUERY.getCode(),
                        AggregateFeatureSourceEnum.CDP_LABEL.getCode()));
            } else {
                //  分类不为空，也不是CDP，特征没有分类id，那么这里就只需要查询索骥就好了
                strategies = Collections.singletonList(featureAggregateQueryStrategyContext.getStrategy(AggregateQuerySceneEnum.GENERAL_QUERY.getCode(),
                        AggregateFeatureSourceEnum.SUOJI_FIELD.getCode()));
            }
        }
        return strategies;
    }


    /**
     * 跨数据源聚合查询（使用 offset/limit 方式）
     * <p>
     * 该方法从多个数据源（策略）中获取数据，并将它们合并为一个统一的分页结果。
     * 每个策略可能包含不同数量的记录，需要计算正确的偏移量和获取数量。
     *
     * @param req        包含查询条件和分页参数的请求对象
     * @param strategies 要查询的策略列表
     * @return 聚合查询结果的分页信息
     */
    private PageInfoVO<AggregationQueryResultDTO> aggregateQueryPageBySource(BaseAggregationQueryReqDTO req,
                                                                             List<FeatureAggregateQueryStrategy> strategies) {

        if (CollectionUtil.isEmpty(strategies)) {
            return FeaturePageUtil.convertPageToPageInfo(0, Collections.emptyList());
        }

        // 验证并规范化分页参数
        int offset = Math.max(0, req.getOffset());
        int limit = Math.max(1, req.getLimit());

        log.info("执行跨数据源聚合查询: req={}", req);

        // 初始化结果集合和计数器
        List<AggregationQueryResultDTO> resultList = new ArrayList<>(limit);
        int totalRecords = 0;
        int currentGlobalOffset = 0;
        int remainingLimit = limit;

        // 遍历所有策略，收集数据
        for (FeatureAggregateQueryStrategy strategy : strategies) {
            // 如果已经收集足够的记录，只需计算总数
            if (remainingLimit <= 0) {
                totalRecords += calculateStrategyTotalCount(strategy, req);
                continue;
            }

            // 获取当前策略的记录总数
            int strategyTotalCount = calculateStrategyTotalCount(strategy, req);
            if (strategyTotalCount == 0) {
                log.debug("策略 {} 没有数据，跳过", strategy.getClass().getSimpleName());
                continue;
            }

            // 计算当前策略的数据范围
            StrategyDataRange dataRange = calculateStrategyDataRange(
                    currentGlobalOffset, strategyTotalCount, offset, limit);

            // 更新全局总记录数
            totalRecords += strategyTotalCount;

            // 如果没有需要获取的数据，继续下一个策略
            if (!dataRange.hasIntersection()) {
                currentGlobalOffset += strategyTotalCount;
                continue;
            }

            // 根据策略的分页模式获取数据
            List<AggregationQueryResultDTO> strategyData = fetchDataFromStrategyWithPagingMode(
                    strategy, req, dataRange.getInternalOffset(), dataRange.getFetchCount());

            // 添加到结果集合
            resultList.addAll(strategyData);

            // 更新剩余需要获取的记录数
            remainingLimit -= strategyData.size();

            // 更新全局偏移量
            currentGlobalOffset += strategyTotalCount;
        }

        log.info("跨数据源聚合查询完成: 获取到 {} 条记录，总记录数 {}", resultList.size(), totalRecords);

        return buildPageResponse(resultList, totalRecords);
    }

    /**
     * 计算策略的总记录数
     *
     * @param strategy 查询策略
     * @param req      查询请求
     * @return 总记录数
     */
    private int calculateStrategyTotalCount(FeatureAggregateQueryStrategy strategy, BaseAggregationQueryReqDTO req) {
        int count = strategy.getCount(req);
        log.debug("策略 {} 总记录数: {}", strategy.getClass().getSimpleName(), count);
        return count;
    }

    /**
     * 计算策略数据范围
     * <p>
     * 计算全局偏移量和策略内部偏移量的交集，确定需要从当前策略获取的数据范围。
     *
     * @param globalOffset  当前全局偏移量
     * @param strategyCount 当前策略的总记录数
     * @param requestOffset 请求的偏移量
     * @param requestLimit  请求的限制数
     * @return 策略数据范围对象
     */
    private StrategyDataRange calculateStrategyDataRange(int globalOffset, int strategyCount,
                                                         int requestOffset, int requestLimit) {
        // 计算当前策略的数据范围 [globalOffset, globalOffset + strategyCount)
        int strategyEndOffset = globalOffset + strategyCount;

        // 计算请求的数据范围 [requestOffset, requestOffset + requestLimit)
        int requestEndOffset = requestOffset + requestLimit;

        // 计算两个范围的交集
        int intersectionStart = Math.max(globalOffset, requestOffset);
        int intersectionEnd = Math.min(strategyEndOffset, requestEndOffset);

        // 计算在策略内部的偏移量和获取数量
        int internalOffset = intersectionStart - globalOffset;
        int fetchCount = Math.max(0, intersectionEnd - intersectionStart);

        log.debug("计算数据范围: 策略范围[{}, {}), 请求范围[{}, {}), 交集[{}, {}), 内部偏移={}, 获取数量={}",
                globalOffset, strategyEndOffset, requestOffset, requestEndOffset,
                intersectionStart, intersectionEnd, internalOffset, fetchCount);

        return new StrategyDataRange(intersectionStart, intersectionEnd, internalOffset, fetchCount);
    }

    /**
     * 从策略获取数据
     * <p>
     * 根据计算出的内部偏移量和获取数量，从策略中获取数据。
     *
     * @param strategy       查询策略
     * @param req            原始请求
     * @param internalOffset 策略内部偏移量
     * @param fetchCount     需要获取的记录数
     * @return 获取到的数据列表
     */
    private List<AggregationQueryResultDTO> fetchDataFromStrategy(FeatureAggregateQueryStrategy strategy,
                                                                  BaseAggregationQueryReqDTO req,
                                                                  int internalOffset, int fetchCount) {
        if (fetchCount <= 0) {
            return Collections.emptyList();
        }

        log.debug("从策略 {} 获取数据: 内部偏移={}, 获取数量={}",
                strategy.getClass().getSimpleName(), internalOffset, fetchCount);

        // 创建新的请求对象，设置正确的偏移量和限制
        BaseAggregationQueryReqDTO tempReq = cloneRequestWithNewPaging(req, internalOffset, fetchCount);

        // 调用策略获取数据
        List<AggregationQueryResultDTO> data = strategy.getListByPage(tempReq);

        if (data == null) {
            log.warn("策略 {} 返回了null，转换为空列表", strategy.getClass().getSimpleName());
            return Collections.emptyList();
        }

        log.debug("策略 {} 返回 {} 条数据", strategy.getClass().getSimpleName(), data.size());

        return data;
    }

    /**
     * 创建带有新分页参数的请求对象
     *
     * @param originalReq 原始请求
     * @param offset      新的偏移量
     * @param limit       新的限制数
     * @return 新的请求对象
     */
    private BaseAggregationQueryReqDTO cloneRequestWithNewPaging(BaseAggregationQueryReqDTO originalReq,
                                                                 int offset, int limit) {
        BaseAggregationQueryReqDTO query;
        if (originalReq instanceof HawkAggregationQueryDTO) {
            query = new HawkAggregationQueryDTO();
            BeanUtils.copyProperties(originalReq, query);
        } else if (originalReq instanceof GeneralAggregationQueryDTO) {
            query = new GeneralAggregationQueryDTO();
            BeanUtils.copyProperties(originalReq, query);
        } else {
            throw new IllegalArgumentException("不支持的请求类型: " + originalReq.getClass().getName());
        }

        query.setOffset(offset);
        query.setLimit(limit);

        return query;
    }

    /**
     * 构建分页响应
     *
     * @param data  数据列表
     * @param total 总记录数
     * @return 分页响应对象
     */
    private PageInfoVO<AggregationQueryResultDTO> buildPageResponse(List<AggregationQueryResultDTO> data, int total) {
        return FeaturePageUtil.convertPageToPageInfo(total, data);
    }

    /**
     * 策略数据范围类
     * <p>
     * 用于存储计算出的策略数据范围信息。
     */
    private static class StrategyDataRange {
        private final int intersectionStart;
        private final int intersectionEnd;
        /**
         * -- GETTER --
         * 获取策略内部偏移量
         *
         * @return 内部偏移量
         */
        @Getter
        private final int internalOffset;
        /**
         * -- GETTER --
         * 获取需要获取的记录数
         *
         * @return 获取数量
         */
        @Getter
        private final int fetchCount;

        public StrategyDataRange(int intersectionStart, int intersectionEnd,
                                 int internalOffset, int fetchCount) {
            this.intersectionStart = intersectionStart;
            this.intersectionEnd = intersectionEnd;
            this.internalOffset = internalOffset;
            this.fetchCount = fetchCount;
        }

        /**
         * 判断是否有交集
         *
         * @return 如果有交集返回true，否则返回false
         */
        public boolean hasIntersection() {
            return intersectionStart < intersectionEnd;
        }

    }

}
