package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.service;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.core.AggregationPagingEngine;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.manager.AggregationStrategyManager;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingResponse;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AggregationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 聚合分页查询服务
 * 
 * 提供统一的聚合分页查询入口，封装复杂的策略选择和执行逻辑
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Service
public class AggregationPagingService {

    private final AggregationStrategyManager strategyManager;
    private final AggregationPagingEngine<Object> pagingEngine;

    public AggregationPagingService(AggregationStrategyManager strategyManager) {
        this.strategyManager = strategyManager;
        this.pagingEngine = new AggregationPagingEngine<>();
    }

    /**
     * 执行聚合分页查询
     * 
     * @param request 查询请求
     * @param <T> 结果类型
     * @return 查询响应
     */
    @SuppressWarnings("unchecked")
    public <T> AggregationPagingResponse<T> query(AggregationPagingRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 参数验证和规范化
            validateAndNormalizeRequest(request);

            // 选择合适的策略
            List<AggregationStrategy<?>> strategies = strategyManager.selectStrategies(request);
            if (CollectionUtils.isEmpty(strategies)) {
                log.warn("没有找到支持场景 {} 的策略", request.getSceneName());
                return AggregationPagingResponse.empty();
            }

            log.info("开始聚合查询: 场景={}, 策略数={}, offset={}, limit={}", 
                    request.getSceneName(), strategies.size(), request.getOffset(), request.getLimit());

            // 执行聚合查询
            List<AggregationStrategy<Object>> objectStrategies = (List<AggregationStrategy<Object>>) (List<?>) strategies;
            AggregationPagingResponse<Object> response = pagingEngine.execute(request, objectStrategies);

            // 设置响应信息
            long costTime = System.currentTimeMillis() - startTime;
            response.withCostTime(costTime)
                   .withHasMore(request.getOffset(), request.getLimit());

            log.info("聚合查询完成: 耗时={}ms, 结果数={}, 总数={}", 
                    costTime, response.getSize(), response.getTotal());

            return (AggregationPagingResponse<T>) response;

        } catch (Exception e) {
            long costTime = System.currentTimeMillis() - startTime;
            log.error("聚合查询失败: 场景={}, 耗时={}ms, 错误={}", 
                    request.getSceneName(), costTime, e.getMessage(), e);
            return AggregationPagingResponse.<T>empty().withCostTime(costTime);
        }
    }

    /**
     * 指定策略执行查询
     * 
     * @param request 查询请求
     * @param strategyNames 策略名称列表
     * @param <T> 结果类型
     * @return 查询响应
     */
    @SuppressWarnings("unchecked")
    public <T> AggregationPagingResponse<T> queryWithStrategies(AggregationPagingRequest request, 
                                                               List<String> strategyNames) {
        long startTime = System.currentTimeMillis();
        
        try {
            validateAndNormalizeRequest(request);

            // 根据策略名称获取策略
            List<AggregationStrategy<?>> strategies = strategyManager.getAllStrategies().stream()
                    .filter(strategy -> strategyNames.contains(strategy.getStrategyName()))
                    .collect(java.util.stream.Collectors.toList());

            if (CollectionUtils.isEmpty(strategies)) {
                log.warn("没有找到指定的策略: {}", strategyNames);
                return AggregationPagingResponse.empty();
            }

            log.info("开始指定策略查询: 策略={}, offset={}, limit={}", 
                    strategyNames, request.getOffset(), request.getLimit());

            List<AggregationStrategy<Object>> objectStrategies = (List<AggregationStrategy<Object>>) (List<?>) strategies;
            AggregationPagingResponse<Object> response = pagingEngine.execute(request, objectStrategies);

            long costTime = System.currentTimeMillis() - startTime;
            response.withCostTime(costTime)
                   .withHasMore(request.getOffset(), request.getLimit());

            log.info("指定策略查询完成: 耗时={}ms, 结果数={}, 总数={}", 
                    costTime, response.getSize(), response.getTotal());

            return (AggregationPagingResponse<T>) response;

        } catch (Exception e) {
            long costTime = System.currentTimeMillis() - startTime;
            log.error("指定策略查询失败: 策略={}, 耗时={}ms, 错误={}", 
                    strategyNames, costTime, e.getMessage(), e);
            return AggregationPagingResponse.<T>empty().withCostTime(costTime);
        }
    }

    /**
     * 单一数据源查询
     * 
     * @param request 查询请求
     * @param dataSource 数据源标识
     * @param <T> 结果类型
     * @return 查询响应
     */
    @SuppressWarnings("unchecked")
    public <T> AggregationPagingResponse<T> queryFromDataSource(AggregationPagingRequest request, 
                                                               String dataSource) {
        long startTime = System.currentTimeMillis();
        
        try {
            validateAndNormalizeRequest(request);

            AggregationStrategy<?> strategy = strategyManager.getStrategy(request.getSceneName(), dataSource);
            if (strategy == null) {
                log.warn("没有找到数据源 {} 在场景 {} 的策略", dataSource, request.getSceneName());
                return AggregationPagingResponse.empty();
            }

            log.info("开始单一数据源查询: 数据源={}, offset={}, limit={}", 
                    dataSource, request.getOffset(), request.getLimit());

            List<AggregationStrategy<Object>> strategies = List.of((AggregationStrategy<Object>) strategy);
            AggregationPagingResponse<Object> response = pagingEngine.execute(request, strategies);

            long costTime = System.currentTimeMillis() - startTime;
            response.withCostTime(costTime)
                   .withHasMore(request.getOffset(), request.getLimit());

            log.info("单一数据源查询完成: 耗时={}ms, 结果数={}, 总数={}", 
                    costTime, response.getSize(), response.getTotal());

            return (AggregationPagingResponse<T>) response;

        } catch (Exception e) {
            long costTime = System.currentTimeMillis() - startTime;
            log.error("单一数据源查询失败: 数据源={}, 耗时={}ms, 错误={}", 
                    dataSource, costTime, e.getMessage(), e);
            return AggregationPagingResponse.<T>empty().withCostTime(costTime);
        }
    }

    /**
     * 验证和规范化请求参数
     */
    private void validateAndNormalizeRequest(AggregationPagingRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("查询请求不能为null");
        }

        // 规范化分页参数
        request.normalizePaging();

        // 设置默认场景
        if (request.getSceneName() == null) {
            request.setSceneName("default");
        }

        if (!request.isValidPaging()) {
            throw new IllegalArgumentException("分页参数无效: offset=" + request.getOffset() + ", limit=" + request.getLimit());
        }
    }
}
