package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.example;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AbstractAggregationStrategy;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.AggregationQueryResultDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 示例特征聚合策略实现
 * 
 * 展示如何基于通用框架实现具体的聚合策略
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
public class ExampleFeatureAggregationStrategy extends AbstractAggregationStrategy<AggregationQueryResultDTO, ExampleFeatureAggregationStrategy.ExampleQueryParam> {

    private final ExampleFeatureDao featureDao;

    public ExampleFeatureAggregationStrategy(ExampleFeatureDao featureDao) {
        super(new ExampleParameterConverter());
        this.featureDao = featureDao;
    }

    @Override
    public String getDataSource() {
        return "example_feature";
    }

    @Override
    public int getPriority() {
        return 100;
    }

    @Override
    public boolean supports(AggregationPagingRequest request) {
        // 示例：只支持特定场景
        return "example_scene".equals(request.getSceneName()) || "default".equals(request.getSceneName());
    }

    @Override
    protected long doCount(ExampleQueryParam queryParam) {
        return featureDao.countByParam(queryParam);
    }

    @Override
    protected List<AggregationQueryResultDTO> doQueryByPage(ExampleQueryParam queryParam) {
        List<ExampleFeatureEntity> entities = featureDao.selectByParam(queryParam);
        return convertToResultDTO(entities);
    }

    @Override
    protected List<AggregationQueryResultDTO> doQueryByIdList(ExampleQueryParam queryParam) {
        List<ExampleFeatureEntity> entities = featureDao.selectByIds(queryParam.getIdList());
        return convertToResultDTO(entities);
    }

    /**
     * 转换实体为结果DTO
     */
    private List<AggregationQueryResultDTO> convertToResultDTO(List<ExampleFeatureEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return Collections.emptyList();
        }

        List<AggregationQueryResultDTO> results = new ArrayList<>();
        for (ExampleFeatureEntity entity : entities) {
            AggregationQueryResultDTO dto = new AggregationQueryResultDTO();
            dto.setFeatureName(entity.getName());
            dto.setFeatureDesc(entity.getDescription());
            dto.setFeatureSource(getDataSource());
            dto.setCreateTime(entity.getCreateTime());
            dto.setModifyTime(entity.getModifyTime());
            results.add(dto);
        }
        return results;
    }

    /**
     * 示例查询参数
     */
    @Data
    public static class ExampleQueryParam {
        private String nameLike;
        private List<String> nameList;
        private List<String> idList;
        private Integer categoryId;
        private Integer status;
        private Integer offset;
        private Integer limit;
        private String orderBy;
    }

    /**
     * 示例参数转换器
     */
    public static class ExampleParameterConverter implements ParameterConverter<ExampleQueryParam> {

        @Override
        public ExampleQueryParam convertForCount(AggregationPagingRequest request) {
            ExampleQueryParam param = new ExampleQueryParam();
            setCommonConditions(param, request);
            return param;
        }

        @Override
        public ExampleQueryParam convertForPage(AggregationPagingRequest request) {
            ExampleQueryParam param = new ExampleQueryParam();
            setCommonConditions(param, request);
            
            // 设置分页参数
            param.setOffset(request.getOffset());
            param.setLimit(request.getLimit());
            
            // 设置排序
            if (StringUtils.hasText(request.getOrderBy())) {
                param.setOrderBy(request.getOrderBy() + " " + request.getOrderDirection());
            } else {
                param.setOrderBy("create_time DESC");
            }
            
            return param;
        }

        @Override
        public ExampleQueryParam convertForIdList(AggregationPagingRequest request, List<String> idList) {
            ExampleQueryParam param = new ExampleQueryParam();
            setCommonConditions(param, request);
            param.setIdList(idList);
            return param;
        }

        private void setCommonConditions(ExampleQueryParam param, AggregationPagingRequest request) {
            param.setNameLike(request.getKeyword());
            param.setNameList(request.getNameList());
            param.setCategoryId(request.getCategoryId());
            param.setStatus(request.getStatus());
        }
    }

    /**
     * 示例特征实体
     */
    @Data
    public static class ExampleFeatureEntity {
        private String id;
        private String name;
        private String description;
        private Integer categoryId;
        private Integer status;
        private Long createTime;
        private Long modifyTime;
    }

    /**
     * 示例DAO接口
     */
    public interface ExampleFeatureDao {
        long countByParam(ExampleQueryParam param);
        List<ExampleFeatureEntity> selectByParam(ExampleQueryParam param);
        List<ExampleFeatureEntity> selectByIds(List<String> ids);
    }
}
