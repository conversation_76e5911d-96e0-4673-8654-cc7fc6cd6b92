package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * 聚合分页查询响应
 * 
 * 统一的查询响应模型
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AggregationPagingResponse<T> {

    /**
     * 查询结果数据
     */
    private List<T> data;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 当前页数据量
     */
    private int size;

    /**
     * 是否有更多数据
     */
    private boolean hasMore;

    /**
     * 查询耗时（毫秒）
     */
    private long costTime;

    /**
     * 扩展信息
     */
    private Object extra;

    public AggregationPagingResponse(List<T> data, long total) {
        this.data = data == null ? Collections.emptyList() : data;
        this.total = total;
        this.size = this.data.size();
        this.hasMore = false; // 需要根据具体情况设置
    }

    /**
     * 创建空响应
     * 
     * @param <T> 数据类型
     * @return 空响应
     */
    public static <T> AggregationPagingResponse<T> empty() {
        return new AggregationPagingResponse<>(Collections.emptyList(), 0);
    }

    /**
     * 创建成功响应
     * 
     * @param data 数据列表
     * @param total 总数
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> AggregationPagingResponse<T> success(List<T> data, long total) {
        return new AggregationPagingResponse<>(data, total);
    }

    /**
     * 设置查询耗时
     * 
     * @param costTime 耗时
     * @return 当前对象
     */
    public AggregationPagingResponse<T> withCostTime(long costTime) {
        this.costTime = costTime;
        return this;
    }

    /**
     * 设置扩展信息
     * 
     * @param extra 扩展信息
     * @return 当前对象
     */
    public AggregationPagingResponse<T> withExtra(Object extra) {
        this.extra = extra;
        return this;
    }

    /**
     * 设置是否有更多数据
     * 
     * @param offset 当前偏移量
     * @param limit 分页大小
     * @return 当前对象
     */
    public AggregationPagingResponse<T> withHasMore(int offset, int limit) {
        this.hasMore = (offset + limit) < total;
        return this;
    }

    /**
     * 判断是否为空结果
     * 
     * @return true表示空结果
     */
    public boolean isEmpty() {
        return data == null || data.isEmpty();
    }

    /**
     * 获取实际数据大小
     * 
     * @return 数据大小
     */
    public int getActualSize() {
        return data == null ? 0 : data.size();
    }
}
