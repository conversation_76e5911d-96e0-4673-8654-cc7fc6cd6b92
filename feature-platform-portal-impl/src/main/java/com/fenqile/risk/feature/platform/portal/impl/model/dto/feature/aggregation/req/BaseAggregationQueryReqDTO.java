package com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.req;

import com.fenqile.risk.feature.platform.portal.impl.logic.feature.aggregation.FeatureAggregateQueryServiceLogic;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BaseAggregationQueryReqDTO implements FeatureAggregateQueryServiceLogic.ExtendableRequest {

    /**
     * 字段名，支持模糊匹配
     */
    private String featureNameLike;

    /**
     * 字段名列表，严格匹配
     */
    private List<String> featureNameList;

    /**
     * 字段类目
     */
    private Integer categoryId;

    /**
     * 每一页条数
     */
    private Integer limit;

    /**
     * 查询起始索引
     */
    private Integer offset;


    /**
     * 查询场景名，用于路由
     */
    private String querySceneName;

    /**
     * 扩展参数，用于特定场景的自定义参数
     */
    private Map<String, Object> extParams = new HashMap<>();

    /**
     * 添加扩展参数
     */
    @Override
    public void addExtParam(String key, Object value) {
        this.extParams.put(key, value);
    }

    /**
     * 获取扩展参数
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> T getExtParam(String key) {
        return (T) this.extParams.get(key);
    }
}
