package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.config;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.manager.AggregationStrategyManager;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.service.AggregationPagingService;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AggregationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import java.util.Arrays;
import java.util.Map;

/**
 * 聚合框架配置类
 * 
 * 负责框架的初始化和策略的自动注册
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Configuration
public class AggregationFrameworkConfiguration {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 创建聚合分页服务Bean
     */
    @Bean
    public AggregationPagingService aggregationPagingService(AggregationStrategyManager strategyManager) {
        return new AggregationPagingService(strategyManager);
    }

    /**
     * 应用启动完成后自动注册策略
     */
    @EventListener(ContextRefreshedEvent.class)
    public void registerStrategiesOnStartup() {
        log.info("开始自动注册聚合策略...");

        AggregationStrategyManager strategyManager = applicationContext.getBean(AggregationStrategyManager.class);
        
        // 获取所有策略Bean
        Map<String, AggregationStrategy> strategyBeans = applicationContext.getBeansOfType(AggregationStrategy.class);
        
        for (Map.Entry<String, AggregationStrategy> entry : strategyBeans.entrySet()) {
            String beanName = entry.getKey();
            AggregationStrategy<?> strategy = entry.getValue();
            
            try {
                // 根据策略类型确定支持的场景
                String[] supportedScenes = determineSupportedScenes(strategy);
                
                // 注册策略
                strategyManager.registerStrategy(strategy, Arrays.asList(supportedScenes));
                
                log.info("自动注册策略成功: {} -> {}", beanName, Arrays.toString(supportedScenes));
                
            } catch (Exception e) {
                log.error("自动注册策略失败: {}", beanName, e);
            }
        }

        // 输出注册统计
        AggregationStrategyManager.StrategyStatistics stats = strategyManager.getStatistics();
        log.info("策略自动注册完成: {}", stats);
    }

    /**
     * 根据策略类型确定支持的场景
     */
    private String[] determineSupportedScenes(AggregationStrategy<?> strategy) {
        String strategyName = strategy.getClass().getSimpleName();
        
        // 根据策略名称模式匹配场景
        if (strategyName.contains("Hawk")) {
            return new String[]{"hawk_query", "default"};
        } else if (strategyName.contains("General") || strategyName.contains("Gen")) {
            return new String[]{"general_query", "default"};
        } else if (strategyName.contains("Cdp") || strategyName.contains("CDP")) {
            return new String[]{"general_query", "hawk_query", "default"};
        } else if (strategyName.contains("SuoJi") || strategyName.contains("Suoji")) {
            return new String[]{"general_query", "hawk_query", "default"};
        } else {
            // 默认支持所有场景
            return new String[]{"default"};
        }
    }

    /**
     * 框架配置属性
     */
    public static class AggregationFrameworkProperties {
        
        /**
         * 是否启用自动策略注册
         */
        private boolean autoRegisterStrategies = true;
        
        /**
         * 默认分页大小
         */
        private int defaultPageSize = 20;
        
        /**
         * 最大分页大小
         */
        private int maxPageSize = 1000;
        
        /**
         * 查询超时时间（秒）
         */
        private int queryTimeoutSeconds = 30;
        
        /**
         * 是否启用查询缓存
         */
        private boolean enableQueryCache = false;
        
        /**
         * 缓存过期时间（秒）
         */
        private int cacheExpireSeconds = 300;

        // Getters and Setters
        public boolean isAutoRegisterStrategies() {
            return autoRegisterStrategies;
        }

        public void setAutoRegisterStrategies(boolean autoRegisterStrategies) {
            this.autoRegisterStrategies = autoRegisterStrategies;
        }

        public int getDefaultPageSize() {
            return defaultPageSize;
        }

        public void setDefaultPageSize(int defaultPageSize) {
            this.defaultPageSize = defaultPageSize;
        }

        public int getMaxPageSize() {
            return maxPageSize;
        }

        public void setMaxPageSize(int maxPageSize) {
            this.maxPageSize = maxPageSize;
        }

        public int getQueryTimeoutSeconds() {
            return queryTimeoutSeconds;
        }

        public void setQueryTimeoutSeconds(int queryTimeoutSeconds) {
            this.queryTimeoutSeconds = queryTimeoutSeconds;
        }

        public boolean isEnableQueryCache() {
            return enableQueryCache;
        }

        public void setEnableQueryCache(boolean enableQueryCache) {
            this.enableQueryCache = enableQueryCache;
        }

        public int getCacheExpireSeconds() {
            return cacheExpireSeconds;
        }

        public void setCacheExpireSeconds(int cacheExpireSeconds) {
            this.cacheExpireSeconds = cacheExpireSeconds;
        }
    }
}
