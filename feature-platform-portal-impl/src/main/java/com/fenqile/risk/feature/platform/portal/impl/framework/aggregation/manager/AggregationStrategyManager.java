package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.manager;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AggregationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 聚合策略管理器
 * 
 * 负责策略的注册、发现、选择和管理
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Component
public class AggregationStrategyManager implements ApplicationListener<ContextRefreshedEvent> {

    /**
     * 场景 -> 策略列表的映射
     */
    private final Map<String, List<AggregationStrategy<?>>> sceneStrategyMap = new ConcurrentHashMap<>();

    /**
     * 数据源 -> 策略的映射
     */
    private final Map<String, AggregationStrategy<?>> dataSourceStrategyMap = new ConcurrentHashMap<>();

    /**
     * 所有策略列表
     */
    private final List<AggregationStrategy<?>> allStrategies = new ArrayList<>();

    /**
     * 注册策略
     * 
     * @param strategy 策略实例
     * @param scenes 支持的场景列表
     */
    public void registerStrategy(AggregationStrategy<?> strategy, List<String> scenes) {
        if (strategy == null) {
            log.warn("尝试注册null策略，忽略");
            return;
        }

        // 添加到全局策略列表
        allStrategies.add(strategy);

        // 注册到数据源映射
        dataSourceStrategyMap.put(strategy.getDataSource(), strategy);

        // 注册到场景映射
        if (CollectionUtils.isEmpty(scenes)) {
            scenes = Collections.singletonList("default");
        }

        for (String scene : scenes) {
            sceneStrategyMap.computeIfAbsent(scene, k -> new ArrayList<>()).add(strategy);
        }

        log.info("注册策略: {} -> 数据源: {} -> 场景: {}", 
                strategy.getStrategyName(), strategy.getDataSource(), scenes);
    }

    /**
     * 获取指定场景的所有策略
     * 
     * @param sceneName 场景名称
     * @return 策略列表，按优先级排序
     */
    public List<AggregationStrategy<?>> getStrategies(String sceneName) {
        List<AggregationStrategy<?>> strategies = sceneStrategyMap.get(sceneName);
        if (CollectionUtils.isEmpty(strategies)) {
            log.debug("场景 {} 没有注册的策略", sceneName);
            return Collections.emptyList();
        }

        // 按优先级排序
        return strategies.stream()
                .sorted(Comparator.comparingInt(AggregationStrategy::getPriority))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定场景和数据源的策略
     * 
     * @param sceneName 场景名称
     * @param dataSource 数据源
     * @return 策略实例，可能为null
     */
    public AggregationStrategy<?> getStrategy(String sceneName, String dataSource) {
        List<AggregationStrategy<?>> strategies = getStrategies(sceneName);
        return strategies.stream()
                .filter(strategy -> dataSource.equals(strategy.getDataSource()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据请求自动选择合适的策略
     * 
     * @param request 查询请求
     * @return 支持该请求的策略列表
     */
    public List<AggregationStrategy<?>> selectStrategies(AggregationPagingRequest request) {
        String sceneName = request.getSceneName();
        if (sceneName == null) {
            sceneName = "default";
        }

        List<AggregationStrategy<?>> candidates = getStrategies(sceneName);
        if (CollectionUtils.isEmpty(candidates)) {
            log.warn("场景 {} 没有可用的策略", sceneName);
            return Collections.emptyList();
        }

        // 过滤支持当前请求的策略
        List<AggregationStrategy<?>> supportedStrategies = candidates.stream()
                .filter(strategy -> strategy.supports(request))
                .collect(Collectors.toList());

        log.debug("场景 {} 选择了 {} 个策略: {}", sceneName, supportedStrategies.size(),
                supportedStrategies.stream().map(AggregationStrategy::getStrategyName).collect(Collectors.toList()));

        return supportedStrategies;
    }

    /**
     * 获取所有已注册的策略
     * 
     * @return 策略列表
     */
    public List<AggregationStrategy<?>> getAllStrategies() {
        return new ArrayList<>(allStrategies);
    }

    /**
     * 获取所有支持的场景
     * 
     * @return 场景列表
     */
    public Set<String> getAllScenes() {
        return new HashSet<>(sceneStrategyMap.keySet());
    }

    /**
     * 获取所有支持的数据源
     * 
     * @return 数据源列表
     */
    public Set<String> getAllDataSources() {
        return new HashSet<>(dataSourceStrategyMap.keySet());
    }

    /**
     * 清空所有策略
     */
    public void clearAll() {
        sceneStrategyMap.clear();
        dataSourceStrategyMap.clear();
        allStrategies.clear();
        log.info("清空所有策略");
    }

    /**
     * 获取策略统计信息
     * 
     * @return 统计信息
     */
    public StrategyStatistics getStatistics() {
        return new StrategyStatistics(
                allStrategies.size(),
                sceneStrategyMap.size(),
                dataSourceStrategyMap.size()
        );
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        log.info("应用上下文刷新完成，策略管理器统计: {}", getStatistics());
    }

    /**
     * 策略统计信息
     */
    public static class StrategyStatistics {
        private final int totalStrategies;
        private final int totalScenes;
        private final int totalDataSources;

        public StrategyStatistics(int totalStrategies, int totalScenes, int totalDataSources) {
            this.totalStrategies = totalStrategies;
            this.totalScenes = totalScenes;
            this.totalDataSources = totalDataSources;
        }

        public int getTotalStrategies() {
            return totalStrategies;
        }

        public int getTotalScenes() {
            return totalScenes;
        }

        public int getTotalDataSources() {
            return totalDataSources;
        }

        @Override
        public String toString() {
            return String.format("StrategyStatistics{strategies=%d, scenes=%d, dataSources=%d}",
                    totalStrategies, totalScenes, totalDataSources);
        }
    }
}
