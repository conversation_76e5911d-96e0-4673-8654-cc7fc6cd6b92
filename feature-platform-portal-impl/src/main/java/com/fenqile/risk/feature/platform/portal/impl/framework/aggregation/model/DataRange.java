package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据范围计算结果
 * 
 * 用于跨数据源分页时的范围计算
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataRange {

    /**
     * 交集开始位置
     */
    private long intersectionStart;

    /**
     * 交集结束位置
     */
    private long intersectionEnd;

    /**
     * 策略内部偏移量
     */
    private int internalOffset;

    /**
     * 需要获取的记录数
     */
    private int fetchCount;

    /**
     * 判断是否有交集
     * 
     * @return true表示有交集，需要从该策略获取数据
     */
    public boolean hasIntersection() {
        return intersectionStart < intersectionEnd && fetchCount > 0;
    }

    /**
     * 获取交集大小
     * 
     * @return 交集大小
     */
    public long getIntersectionSize() {
        return Math.max(0, intersectionEnd - intersectionStart);
    }

    /**
     * 创建无交集的数据范围
     * 
     * @return 无交集的数据范围
     */
    public static DataRange noIntersection() {
        return new DataRange(0, 0, 0, 0);
    }

    /**
     * 创建数据范围
     * 
     * @param globalOffset 全局偏移量
     * @param strategyCount 策略记录数
     * @param requestOffset 请求偏移量
     * @param requestLimit 请求限制数
     * @return 数据范围
     */
    public static DataRange calculate(long globalOffset, long strategyCount,
                                    long requestOffset, long requestLimit) {
        // 计算当前策略的数据范围 [globalOffset, globalOffset + strategyCount)
        long strategyEndOffset = globalOffset + strategyCount;

        // 计算请求的数据范围 [requestOffset, requestOffset + requestLimit)
        long requestEndOffset = requestOffset + requestLimit;

        // 计算两个范围的交集
        long intersectionStart = Math.max(globalOffset, requestOffset);
        long intersectionEnd = Math.min(strategyEndOffset, requestEndOffset);

        // 计算在策略内部的偏移量和获取数量
        int internalOffset = (int) Math.max(0, intersectionStart - globalOffset);
        int fetchCount = (int) Math.max(0, intersectionEnd - intersectionStart);

        return new DataRange(intersectionStart, intersectionEnd, internalOffset, fetchCount);
    }

    @Override
    public String toString() {
        return String.format("DataRange{intersection=[%d,%d), internal=%d, fetch=%d, hasIntersection=%s}",
                intersectionStart, intersectionEnd, internalOffset, fetchCount, hasIntersection());
    }
}
