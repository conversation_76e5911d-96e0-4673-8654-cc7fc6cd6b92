package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.example;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.core.AggregationPagingEngine;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingResponse;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AggregationStrategy;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.util.PagingParameterConverter;
import com.fenqile.risk.feature.platform.portal.impl.model.dto.feature.aggregation.AggregationQueryResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 分页模式测试示例
 * 
 * 展示不同分页模式的策略如何协同工作
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Component
public class PagingModeTestExample {

    /**
     * 测试混合分页模式
     */
    public void testMixedPagingModes() {
        log.info("=== 测试混合分页模式 ===");

        // 创建不同分页模式的策略
        ExampleFeatureAggregationStrategy offsetLimitStrategy = createOffsetLimitStrategy();
        PageSizeAggregationStrategy pageSizeStrategy = createPageSizeStrategy();

        // 创建聚合引擎
        AggregationPagingEngine<AggregationQueryResultDTO> engine = new AggregationPagingEngine<>();

        // 创建查询请求
        AggregationPagingRequest request = new AggregationPagingRequest()
                .setSceneName("mixed_test")
                .setKeyword("特征")
                .setOffset(15)  // 从第15条开始
                .setLimit(10);  // 获取10条

        // 组合策略列表
        List<AggregationStrategy<AggregationQueryResultDTO>> strategies = Arrays.asList(
                (AggregationStrategy<AggregationQueryResultDTO>) offsetLimitStrategy,
                (AggregationStrategy<AggregationQueryResultDTO>) pageSizeStrategy
        );

        // 执行聚合查询
        AggregationPagingResponse<AggregationQueryResultDTO> response = engine.execute(request, strategies);

        // 输出结果
        log.info("混合分页查询结果:");
        log.info("- 总数: {}", response.getTotal());
        log.info("- 当前页数量: {}", response.getSize());
        log.info("- 数据来源分布:");

        for (AggregationQueryResultDTO item : response.getData()) {
            log.info("  * {} (来源: {})", item.getFeatureName(), item.getFeatureSource());
        }
    }

    /**
     * 测试分页参数转换
     */
    public void testPagingParameterConversion() {
        log.info("=== 测试分页参数转换 ===");

        // 测试 offset+limit 转 page+size
        testOffsetToPageConversion();

        // 测试 page+size 转 offset+limit
        testPageToOffsetConversion();

        // 测试实际数据范围计算
        testActualDataRangeCalculation();
    }

    private void testOffsetToPageConversion() {
        log.info("--- offset+limit 转 page+size ---");

        int[][] testCases = {
                {0, 20},    // 第1页
                {20, 20},   // 第2页
                {15, 10},   // 不对齐的情况
                {100, 25}   // 第5页
        };

        for (int[] testCase : testCases) {
            int offset = testCase[0];
            int limit = testCase[1];

            PagingParameterConverter.PageSizeParam pageParam = 
                    PagingParameterConverter.convertToPageSize(offset, limit);

            log.info("offset={}, limit={} -> pageNo={}, pageSize={}", 
                    offset, limit, pageParam.getPageNo(), pageParam.getPageSize());
        }
    }

    private void testPageToOffsetConversion() {
        log.info("--- page+size 转 offset+limit ---");

        int[][] testCases = {
                {1, 20},    // 第1页
                {2, 20},    // 第2页
                {3, 15},    // 第3页，每页15条
                {5, 25}     // 第5页，每页25条
        };

        for (int[] testCase : testCases) {
            int pageNo = testCase[0];
            int pageSize = testCase[1];

            PagingParameterConverter.OffsetLimitParam offsetParam = 
                    PagingParameterConverter.convertToOffsetLimit(pageNo, pageSize);

            log.info("pageNo={}, pageSize={} -> offset={}, limit={}", 
                    pageNo, pageSize, offsetParam.getOffset(), offsetParam.getLimit());
        }
    }

    private void testActualDataRangeCalculation() {
        log.info("--- 实际数据范围计算 ---");

        // 模拟场景：请求offset=15, limit=10，但页码策略只能按页查询
        int requestOffset = 15;
        int requestLimit = 10;

        // 页码策略实际查询的是第2页（pageNo=2, pageSize=20）
        int actualPageNo = 2;
        int actualPageSize = 20;

        PagingParameterConverter.ActualDataRange actualRange = 
                PagingParameterConverter.calculateActualRange(
                        requestOffset, requestLimit, actualPageNo, actualPageSize);

        log.info("请求范围: offset={}, limit={}", requestOffset, requestLimit);
        log.info("实际查询: pageNo={}, pageSize={}", actualPageNo, actualPageSize);
        log.info("实际数据范围: {}", actualRange);
        log.info("有效数据: 从索引{}到{}, 共{}条", 
                actualRange.getValidStartIndex(), 
                actualRange.getValidEndIndex(), 
                actualRange.getValidCount());
    }

    /**
     * 测试边界情况
     */
    public void testEdgeCases() {
        log.info("=== 测试边界情况 ===");

        // 测试第一页
        testFirstPage();

        // 测试最后一页
        testLastPage();

        // 测试空结果
        testEmptyResult();

        // 测试单条数据
        testSingleRecord();
    }

    private void testFirstPage() {
        log.info("--- 测试第一页 ---");
        
        AggregationPagingRequest request = new AggregationPagingRequest()
                .setOffset(0)
                .setLimit(5);

        log.info("第一页查询: offset={}, limit={}", request.getOffset(), request.getLimit());
        
        // 模拟执行...
    }

    private void testLastPage() {
        log.info("--- 测试最后一页 ---");
        
        // 假设总共103条数据，每页20条，最后一页只有3条
        AggregationPagingRequest request = new AggregationPagingRequest()
                .setOffset(100)
                .setLimit(20);

        log.info("最后一页查询: offset={}, limit={}", request.getOffset(), request.getLimit());
        
        // 模拟执行...
    }

    private void testEmptyResult() {
        log.info("--- 测试空结果 ---");
        
        AggregationPagingRequest request = new AggregationPagingRequest()
                .setOffset(1000)  // 超出数据范围
                .setLimit(20);

        log.info("空结果查询: offset={}, limit={}", request.getOffset(), request.getLimit());
        
        // 模拟执行...
    }

    private void testSingleRecord() {
        log.info("--- 测试单条数据 ---");
        
        AggregationPagingRequest request = new AggregationPagingRequest()
                .setOffset(0)
                .setLimit(1);

        log.info("单条数据查询: offset={}, limit={}", request.getOffset(), request.getLimit());
        
        // 模拟执行...
    }

    /**
     * 创建支持offset+limit的策略
     */
    private ExampleFeatureAggregationStrategy createOffsetLimitStrategy() {
        // 使用模拟DAO
        ExampleFeatureAggregationStrategy.ExampleFeatureDao mockDao = 
                new ExampleFeatureAggregationStrategy.ExampleFeatureDao() {
            @Override
            public long countByParam(ExampleFeatureAggregationStrategy.ExampleQueryParam param) {
                return 50; // 模拟50条数据
            }

            @Override
            public List<ExampleFeatureAggregationStrategy.ExampleFeatureEntity> selectByParam(
                    ExampleFeatureAggregationStrategy.ExampleQueryParam param) {
                // 模拟返回数据
                return java.util.Collections.emptyList();
            }

            @Override
            public List<ExampleFeatureAggregationStrategy.ExampleFeatureEntity> selectByIds(List<String> ids) {
                return java.util.Collections.emptyList();
            }
        };

        return new ExampleFeatureAggregationStrategy(mockDao);
    }

    /**
     * 创建支持page+size的策略
     */
    private PageSizeAggregationStrategy createPageSizeStrategy() {
        PageSizeAggregationStrategy.PageSizeFeatureDao mockDao = 
                new PageSizeAggregationStrategy.MockPageSizeFeatureDao();

        return new PageSizeAggregationStrategy(mockDao);
    }

    /**
     * 运行所有测试
     */
    public void runAllTests() {
        try {
            testMixedPagingModes();
            testPagingParameterConversion();
            testEdgeCases();
            
            log.info("=== 所有分页模式测试完成 ===");
        } catch (Exception e) {
            log.error("测试过程中发生错误", e);
        }
    }
}
