package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * 抽象聚合查询策略
 * 
 * 提供通用的策略实现模板，子类只需实现具体的查询逻辑
 * 
 * @param <T> 查询结果类型
 * @param <P> 查询参数类型
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
public abstract class AbstractAggregationStrategy<T, P> implements AggregationStrategy<T> {

    /**
     * 参数转换器
     */
    private final ParameterConverter<P> parameterConverter;

    protected AbstractAggregationStrategy(ParameterConverter<P> parameterConverter) {
        this.parameterConverter = parameterConverter;
    }

    @Override
    public String getStrategyName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public boolean supports(AggregationPagingRequest request) {
        // 默认支持所有场景，子类可以重写
        return true;
    }

    @Override
    public long count(AggregationPagingRequest request) {
        try {
            P queryParam = parameterConverter.convertForCount(request);
            if (queryParam == null) {
                log.warn("策略 {} 计数查询参数转换失败, 返回0", getStrategyName());
                return 0;
            }
            
            long count = doCount(queryParam);
            log.debug("策略 {} 计数查询完成, 结果: {}", getStrategyName(), count);
            return count;
        } catch (Exception e) {
            log.error("策略 {} 计数查询失败: {}", getStrategyName(), e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<T> queryByPage(AggregationPagingRequest request) {
        try {
            P queryParam = parameterConverter.convertForPage(request);
            if (queryParam == null) {
                log.warn("策略 {} 分页查询参数转换失败, 返回空列表", getStrategyName());
                return Collections.emptyList();
            }
            
            List<T> results = doQueryByPage(queryParam);
            if (results == null) {
                log.warn("策略 {} 返回null结果, 转换为空列表", getStrategyName());
                return Collections.emptyList();
            }
            
            log.debug("策略 {} 分页查询完成, 结果数: {}", getStrategyName(), results.size());
            return results;
        } catch (Exception e) {
            log.error("策略 {} 分页查询失败: {}", getStrategyName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<T> queryByIdList(AggregationPagingRequest request, List<String> idList) {
        try {
            P queryParam = parameterConverter.convertForIdList(request, idList);
            if (queryParam == null) {
                log.warn("策略 {} ID列表查询参数转换失败, 返回空列表", getStrategyName());
                return Collections.emptyList();
            }
            
            List<T> results = doQueryByIdList(queryParam);
            if (results == null) {
                log.warn("策略 {} ID列表查询返回null结果, 转换为空列表", getStrategyName());
                return Collections.emptyList();
            }
            
            log.debug("策略 {} ID列表查询完成, 结果数: {}", getStrategyName(), results.size());
            return results;
        } catch (Exception e) {
            log.error("策略 {} ID列表查询失败: {}", getStrategyName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 执行计数查询
     * 
     * @param queryParam 查询参数
     * @return 记录总数
     */
    protected abstract long doCount(P queryParam);

    /**
     * 执行分页查询
     * 
     * @param queryParam 查询参数
     * @return 查询结果
     */
    protected abstract List<T> doQueryByPage(P queryParam);

    /**
     * 执行ID列表查询
     * 
     * @param queryParam 查询参数
     * @return 查询结果
     */
    protected List<T> doQueryByIdList(P queryParam) {
        // 默认不支持，子类可以重写
        throw new UnsupportedOperationException("queryByIdList not supported by " + getStrategyName());
    }

    /**
     * 获取参数转换器
     * 
     * @return 参数转换器
     */
    protected ParameterConverter<P> getParameterConverter() {
        return parameterConverter;
    }

    /**
     * 参数转换器接口
     * 
     * @param <P> 参数类型
     */
    public interface ParameterConverter<P> {
        
        /**
         * 转换计数查询参数
         * 
         * @param request 原始请求
         * @return 转换后的参数
         */
        P convertForCount(AggregationPagingRequest request);
        
        /**
         * 转换分页查询参数
         * 
         * @param request 原始请求
         * @return 转换后的参数
         */
        P convertForPage(AggregationPagingRequest request);
        
        /**
         * 转换ID列表查询参数
         * 
         * @param request 原始请求
         * @param idList ID列表
         * @return 转换后的参数
         */
        default P convertForIdList(AggregationPagingRequest request, List<String> idList) {
            throw new UnsupportedOperationException("convertForIdList not implemented");
        }
    }
}
