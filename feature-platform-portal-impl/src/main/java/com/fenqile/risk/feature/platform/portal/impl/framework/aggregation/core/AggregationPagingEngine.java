package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.core;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingResponse;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.DataRange;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AggregationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 通用聚合分页查询引擎
 * 
 * 核心功能：
 * 1. 跨数据源聚合查询
 * 2. 统一分页计算
 * 3. 策略组合执行
 * 4. 结果合并处理
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
public class AggregationPagingEngine<T> {

    /**
     * 执行聚合分页查询
     * 
     * @param request 聚合查询请求
     * @param strategies 查询策略列表
     * @return 聚合查询结果
     */
    public AggregationPagingResponse<T> execute(AggregationPagingRequest request, 
                                               List<AggregationStrategy<T>> strategies) {
        
        if (CollectionUtils.isEmpty(strategies)) {
            return createEmptyResponse();
        }

        // 验证并规范化分页参数
        int offset = Math.max(0, request.getOffset());
        int limit = Math.max(1, request.getLimit());

        log.info("执行聚合分页查询: offset={}, limit={}, strategies={}", 
                offset, limit, strategies.size());

        // 初始化结果收集器
        List<T> resultList = new ArrayList<>(limit);
        long totalRecords = 0;
        int currentGlobalOffset = 0;
        int remainingLimit = limit;

        // 遍历所有策略，收集数据
        for (AggregationStrategy<T> strategy : strategies) {
            // 如果已经收集足够的记录，只需计算总数
            if (remainingLimit <= 0) {
                totalRecords += calculateStrategyTotalCount(strategy, request);
                continue;
            }

            // 获取当前策略的记录总数
            long strategyTotalCount = calculateStrategyTotalCount(strategy, request);
            if (strategyTotalCount == 0) {
                log.debug("策略 {} 没有数据，跳过", strategy.getStrategyName());
                continue;
            }

            // 计算当前策略的数据范围
            DataRange dataRange = calculateDataRange(
                    currentGlobalOffset, strategyTotalCount, offset, limit);

            // 更新全局总记录数
            totalRecords += strategyTotalCount;

            // 如果没有需要获取的数据，继续下一个策略
            if (!dataRange.hasIntersection()) {
                currentGlobalOffset += strategyTotalCount;
                continue;
            }

            // 从当前策略获取数据
            List<T> strategyData = fetchDataFromStrategy(
                    strategy, request, dataRange.getInternalOffset(), dataRange.getFetchCount());

            // 添加到结果集合
            resultList.addAll(strategyData);

            // 更新剩余需要获取的记录数
            remainingLimit -= strategyData.size();

            // 更新全局偏移量
            currentGlobalOffset += strategyTotalCount;
        }

        log.info("聚合分页查询完成: 获取到 {} 条记录，总记录数 {}", resultList.size(), totalRecords);

        return createResponse(resultList, totalRecords);
    }

    /**
     * 计算策略的总记录数
     */
    private long calculateStrategyTotalCount(AggregationStrategy<T> strategy, 
                                           AggregationPagingRequest request) {
        try {
            long count = strategy.count(request);
            log.debug("策略 {} 总记录数: {}", strategy.getStrategyName(), count);
            return count;
        } catch (Exception e) {
            log.error("策略 {} 计数失败: {}", strategy.getStrategyName(), e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 计算数据范围
     */
    private DataRange calculateDataRange(int globalOffset, long strategyCount,
                                       int requestOffset, int requestLimit) {
        // 计算当前策略的数据范围 [globalOffset, globalOffset + strategyCount)
        long strategyEndOffset = globalOffset + strategyCount;

        // 计算请求的数据范围 [requestOffset, requestOffset + requestLimit)
        long requestEndOffset = requestOffset + requestLimit;

        // 计算两个范围的交集
        long intersectionStart = Math.max(globalOffset, requestOffset);
        long intersectionEnd = Math.min(strategyEndOffset, requestEndOffset);

        // 计算在策略内部的偏移量和获取数量
        int internalOffset = (int) (intersectionStart - globalOffset);
        int fetchCount = (int) Math.max(0, intersectionEnd - intersectionStart);

        log.debug("计算数据范围: 策略范围[{}, {}), 请求范围[{}, {}), 交集[{}, {}), 内部偏移={}, 获取数量={}",
                globalOffset, strategyEndOffset, requestOffset, requestEndOffset,
                intersectionStart, intersectionEnd, internalOffset, fetchCount);

        return new DataRange(intersectionStart, intersectionEnd, internalOffset, fetchCount);
    }

    /**
     * 从策略获取数据
     */
    private List<T> fetchDataFromStrategy(AggregationStrategy<T> strategy,
                                        AggregationPagingRequest request,
                                        int internalOffset, int fetchCount) {
        if (fetchCount <= 0) {
            return Collections.emptyList();
        }

        log.debug("从策略 {} 获取数据: 内部偏移={}, 获取数量={}",
                strategy.getStrategyName(), internalOffset, fetchCount);

        try {
            // 创建新的请求对象，设置正确的偏移量和限制
            AggregationPagingRequest tempRequest = request.cloneWithNewPaging(internalOffset, fetchCount);

            // 调用策略获取数据
            List<T> data = strategy.queryByPage(tempRequest);

            if (data == null) {
                log.warn("策略 {} 返回了null，转换为空列表", strategy.getStrategyName());
                return Collections.emptyList();
            }

            log.debug("策略 {} 返回 {} 条数据", strategy.getStrategyName(), data.size());
            return data;
        } catch (Exception e) {
            log.error("策略 {} 查询失败: {}", strategy.getStrategyName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 创建空响应
     */
    private AggregationPagingResponse<T> createEmptyResponse() {
        return new AggregationPagingResponse<>(Collections.emptyList(), 0);
    }

    /**
     * 创建响应
     */
    private AggregationPagingResponse<T> createResponse(List<T> data, long total) {
        return new AggregationPagingResponse<>(data, total);
    }
}
