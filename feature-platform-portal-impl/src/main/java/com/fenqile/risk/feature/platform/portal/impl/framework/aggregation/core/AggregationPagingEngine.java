package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.core;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingResponse;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.DataRange;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AggregationStrategy;
import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.util.PagingParameterConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 通用聚合分页查询引擎
 * 
 * 核心功能：
 * 1. 跨数据源聚合查询
 * 2. 统一分页计算
 * 3. 策略组合执行
 * 4. 结果合并处理
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
public class AggregationPagingEngine<T> {

    /**
     * 执行聚合分页查询
     * 
     * @param request 聚合查询请求
     * @param strategies 查询策略列表
     * @return 聚合查询结果
     */
    public AggregationPagingResponse<T> execute(AggregationPagingRequest request, 
                                               List<AggregationStrategy<T>> strategies) {
        
        if (CollectionUtils.isEmpty(strategies)) {
            return createEmptyResponse();
        }

        // 验证并规范化分页参数
        int offset = Math.max(0, request.getOffset());
        int limit = Math.max(1, request.getLimit());

        log.info("执行聚合分页查询: offset={}, limit={}, strategies={}", 
                offset, limit, strategies.size());

        // 初始化结果收集器
        List<T> resultList = new ArrayList<>(limit);
        long totalRecords = 0;
        int currentGlobalOffset = 0;
        int remainingLimit = limit;

        // 遍历所有策略，收集数据
        for (AggregationStrategy<T> strategy : strategies) {
            // 如果已经收集足够的记录，只需计算总数
            if (remainingLimit <= 0) {
                totalRecords += calculateStrategyTotalCount(strategy, request);
                continue;
            }

            // 获取当前策略的记录总数
            long strategyTotalCount = calculateStrategyTotalCount(strategy, request);
            if (strategyTotalCount == 0) {
                log.debug("策略 {} 没有数据，跳过", strategy.getStrategyName());
                continue;
            }

            // 计算当前策略的数据范围
            DataRange dataRange = calculateDataRange(
                    currentGlobalOffset, strategyTotalCount, offset, limit);

            // 更新全局总记录数
            totalRecords += strategyTotalCount;

            // 如果没有需要获取的数据，继续下一个策略
            if (!dataRange.hasIntersection()) {
                currentGlobalOffset += strategyTotalCount;
                continue;
            }

            // 从当前策略获取数据
            List<T> strategyData = fetchDataFromStrategy(
                    strategy, request, dataRange.getInternalOffset(), dataRange.getFetchCount());

            // 添加到结果集合
            resultList.addAll(strategyData);

            // 更新剩余需要获取的记录数
            remainingLimit -= strategyData.size();

            // 更新全局偏移量
            currentGlobalOffset += strategyTotalCount;
        }

        log.info("聚合分页查询完成: 获取到 {} 条记录，总记录数 {}", resultList.size(), totalRecords);

        return createResponse(resultList, totalRecords);
    }

    /**
     * 计算策略的总记录数
     */
    private long calculateStrategyTotalCount(AggregationStrategy<T> strategy, 
                                           AggregationPagingRequest request) {
        try {
            long count = strategy.count(request);
            log.debug("策略 {} 总记录数: {}", strategy.getStrategyName(), count);
            return count;
        } catch (Exception e) {
            log.error("策略 {} 计数失败: {}", strategy.getStrategyName(), e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 计算数据范围
     */
    private DataRange calculateDataRange(int globalOffset, long strategyCount,
                                       int requestOffset, int requestLimit) {
        // 计算当前策略的数据范围 [globalOffset, globalOffset + strategyCount)
        long strategyEndOffset = globalOffset + strategyCount;

        // 计算请求的数据范围 [requestOffset, requestOffset + requestLimit)
        long requestEndOffset = requestOffset + requestLimit;

        // 计算两个范围的交集
        long intersectionStart = Math.max(globalOffset, requestOffset);
        long intersectionEnd = Math.min(strategyEndOffset, requestEndOffset);

        // 计算在策略内部的偏移量和获取数量
        int internalOffset = (int) (intersectionStart - globalOffset);
        int fetchCount = (int) Math.max(0, intersectionEnd - intersectionStart);

        log.debug("计算数据范围: 策略范围[{}, {}), 请求范围[{}, {}), 交集[{}, {}), 内部偏移={}, 获取数量={}",
                globalOffset, strategyEndOffset, requestOffset, requestEndOffset,
                intersectionStart, intersectionEnd, internalOffset, fetchCount);

        return new DataRange(intersectionStart, intersectionEnd, internalOffset, fetchCount);
    }

    /**
     * 从策略获取数据
     */
    private List<T> fetchDataFromStrategy(AggregationStrategy<T> strategy,
                                        AggregationPagingRequest request,
                                        int internalOffset, int fetchCount) {
        if (fetchCount <= 0) {
            return Collections.emptyList();
        }

        log.debug("从策略 {} 获取数据: 内部偏移={}, 获取数量={}, 分页模式={}",
                strategy.getStrategyName(), internalOffset, fetchCount, strategy.getPagingMode());

        try {
            List<T> data;

            // 根据策略的分页模式处理
            if (strategy.getPagingMode() == AggregationStrategy.PagingMode.PAGE_SIZE) {
                data = fetchDataFromPageSizeStrategy(strategy, request, internalOffset, fetchCount);
            } else {
                // OFFSET_LIMIT 或 HYBRID 模式
                data = fetchDataFromOffsetLimitStrategy(strategy, request, internalOffset, fetchCount);
            }

            if (data == null) {
                log.warn("策略 {} 返回了null，转换为空列表", strategy.getStrategyName());
                return Collections.emptyList();
            }

            log.debug("策略 {} 返回 {} 条数据", strategy.getStrategyName(), data.size());
            return data;
        } catch (Exception e) {
            log.error("策略 {} 查询失败: {}", strategy.getStrategyName(), e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 从支持OFFSET_LIMIT模式的策略获取数据
     */
    private List<T> fetchDataFromOffsetLimitStrategy(AggregationStrategy<T> strategy,
                                                   AggregationPagingRequest request,
                                                   int internalOffset, int fetchCount) {
        // 创建新的请求对象，设置正确的偏移量和限制
        AggregationPagingRequest tempRequest = request.cloneWithNewPaging(internalOffset, fetchCount);
        return strategy.queryByPage(tempRequest);
    }

    /**
     * 从只支持PAGE_SIZE模式的策略获取数据（支持多次查询）
     */
    private List<T> fetchDataFromPageSizeStrategy(AggregationStrategy<T> strategy,
                                                AggregationPagingRequest request,
                                                int internalOffset, int fetchCount) {
        List<T> result = new ArrayList<>();
        int remaining = fetchCount;
        int currentOffset = internalOffset;

        log.debug("开始页码模式多次查询: 起始offset={}, 需要获取={}条", internalOffset, fetchCount);

        while (remaining > 0) {
            // 计算当前需要查询的页码和页大小
            // 使用固定页大小以提高缓存效率
            int pageSize = Math.min(remaining * 2, 100); // 适当放大页大小，减少查询次数
            int pageNo = (currentOffset / pageSize) + 1;

            // 创建请求
            AggregationPagingRequest tempRequest = request.cloneWithNewPaging(0, pageSize);
            tempRequest.addExtParam("pageNo", pageNo);
            tempRequest.addExtParam("pageSize", pageSize);

            log.debug("页码查询: pageNo={}, pageSize={}, 剩余需要={}条", pageNo, pageSize, remaining);

            // 调用策略查询
            List<T> pageData = strategy.queryByPage(tempRequest);

            if (pageData == null || pageData.isEmpty()) {
                log.debug("页码查询返回空数据，结束查询");
                break;
            }

            // 计算当前页的实际数据范围
            int pageStartOffset = (pageNo - 1) * pageSize;
            int validStartIndex = Math.max(0, currentOffset - pageStartOffset);
            int validEndIndex = Math.min(pageData.size(), validStartIndex + remaining);

            if (validStartIndex < pageData.size() && validStartIndex < validEndIndex) {
                List<T> validData = pageData.subList(validStartIndex, validEndIndex);
                result.addAll(validData);

                log.debug("页码数据提取: 页面{}条数据, 提取范围[{},{}), 提取{}条",
                        pageData.size(), validStartIndex, validEndIndex, validData.size());

                // 更新状态
                remaining -= validData.size();
                currentOffset += validData.size();
            }

            // 如果当前页数据不足，说明已经到末尾
            if (pageData.size() < pageSize) {
                log.debug("当前页数据不足，已到末尾");
                break;
            }
        }

        log.debug("页码模式查询完成: 总共获取{}条数据", result.size());
        return result;
    }

    /**
     * 创建空响应
     */
    private AggregationPagingResponse<T> createEmptyResponse() {
        return new AggregationPagingResponse<>(Collections.emptyList(), 0);
    }

    /**
     * 创建响应
     */
    private AggregationPagingResponse<T> createResponse(List<T> data, long total) {
        return new AggregationPagingResponse<>(data, total);
    }
}
