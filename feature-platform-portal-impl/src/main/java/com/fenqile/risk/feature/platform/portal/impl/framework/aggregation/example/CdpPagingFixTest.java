package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.example;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * CDP分页问题修复测试
 * 
 * 验证CDP策略在PAGE_SIZE模式下的多次查询逻辑
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
@Component
public class CdpPagingFixTest {

    /**
     * 测试修复前后的差异
     */
    public void testCdpPagingFix() {
        log.info("=== CDP分页问题修复测试 ===");
        
        // 测试场景1：不对齐的分页请求
        testMisalignedPaging();
        
        // 测试场景2：跨多页的请求
        testCrossPagePaging();
        
        // 测试场景3：边界情况
        testBoundaryConditions();
    }

    /**
     * 测试不对齐的分页请求
     */
    private void testMisalignedPaging() {
        log.info("--- 测试不对齐的分页请求 ---");
        
        // 场景：请求offset=15, limit=10
        int requestOffset = 15;
        int requestLimit = 10;
        
        log.info("请求: offset={}, limit={} (需要第15-24条数据)", requestOffset, requestLimit);
        
        // 修复前的逻辑（有问题）
        testOldLogic(requestOffset, requestLimit);
        
        // 修复后的逻辑（正确）
        testNewLogic(requestOffset, requestLimit);
    }

    /**
     * 测试跨多页的请求
     */
    private void testCrossPagePaging() {
        log.info("--- 测试跨多页的请求 ---");
        
        // 场景：请求offset=45, limit=30 (跨越多页)
        int requestOffset = 45;
        int requestLimit = 30;
        
        log.info("请求: offset={}, limit={} (需要第45-74条数据)", requestOffset, requestLimit);
        
        testOldLogic(requestOffset, requestLimit);
        testNewLogic(requestOffset, requestLimit);
    }

    /**
     * 测试边界情况
     */
    private void testBoundaryConditions() {
        log.info("--- 测试边界情况 ---");
        
        // 边界情况1：第一页
        testBoundaryCase(0, 10, "第一页");
        
        // 边界情况2：单条数据
        testBoundaryCase(5, 1, "单条数据");
        
        // 边界情况3：大批量数据
        testBoundaryCase(100, 50, "大批量数据");
    }

    /**
     * 模拟修复前的逻辑（有问题的逻辑）
     */
    private void testOldLogic(int offset, int limit) {
        log.info("【修复前】简单转换逻辑:");
        
        // 旧逻辑：直接转换
        int pageSize = limit;
        int pageNo = (offset / limit) + 1;
        
        log.info("  转换结果: pageNo={}, pageSize={}", pageNo, pageSize);
        
        // 模拟查询结果
        int actualStartOffset = (pageNo - 1) * pageSize;
        int actualEndOffset = actualStartOffset + pageSize;
        
        log.info("  实际查询范围: [{}, {})", actualStartOffset, actualEndOffset);
        log.info("  请求范围: [{}, {})", offset, offset + limit);
        
        // 计算覆盖情况
        int coverStart = Math.max(offset, actualStartOffset);
        int coverEnd = Math.min(offset + limit, actualEndOffset);
        int coveredCount = Math.max(0, coverEnd - coverStart);
        int missedCount = limit - coveredCount;
        
        if (missedCount > 0) {
            log.warn("  ❌ 数据丢失: 丢失{}条数据!", missedCount);
        } else {
            log.info("  ✅ 数据完整");
        }
    }

    /**
     * 模拟修复后的逻辑（正确的逻辑）
     */
    private void testNewLogic(int offset, int limit) {
        log.info("【修复后】多次查询逻辑:");
        
        int remaining = limit;
        int currentOffset = offset;
        int totalQueries = 0;
        int totalFetched = 0;
        
        while (remaining > 0) {
            totalQueries++;
            
            // 使用固定页大小
            int pageSize = Math.min(remaining * 2, 100);
            int pageNo = (currentOffset / pageSize) + 1;
            
            log.info("  第{}次查询: pageNo={}, pageSize={}", totalQueries, pageNo, pageSize);
            
            // 模拟查询结果
            int pageStartOffset = (pageNo - 1) * pageSize;
            int validStartIndex = Math.max(0, currentOffset - pageStartOffset);
            int validEndIndex = Math.min(pageSize, validStartIndex + remaining);
            int validCount = Math.max(0, validEndIndex - validStartIndex);
            
            log.info("    页面范围: [{}, {})", pageStartOffset, pageStartOffset + pageSize);
            log.info("    有效范围: 索引[{}, {}), 获取{}条", validStartIndex, validEndIndex, validCount);
            
            totalFetched += validCount;
            remaining -= validCount;
            currentOffset += validCount;
            
            // 模拟页面数据不足的情况
            if (pageStartOffset + pageSize > 200) { // 假设总共200条数据
                log.info("    到达数据末尾，结束查询");
                break;
            }
        }
        
        log.info("  ✅ 查询完成: 总共{}次查询, 获取{}条数据", totalQueries, totalFetched);
        
        if (totalFetched < limit) {
            log.warn("  ⚠️  数据不足: 请求{}条, 实际获取{}条", limit, totalFetched);
        }
    }

    /**
     * 测试边界情况
     */
    private void testBoundaryCase(int offset, int limit, String caseName) {
        log.info("边界测试 - {}: offset={}, limit={}", caseName, offset, limit);
        
        // 简化测试，只显示关键信息
        int pageSize = Math.min(limit * 2, 100);
        int pageNo = (offset / pageSize) + 1;
        
        log.info("  页码转换: pageNo={}, pageSize={}", pageNo, pageSize);
    }

    /**
     * 性能对比测试
     */
    public void testPerformanceComparison() {
        log.info("=== 性能对比测试 ===");
        
        // 测试不同请求模式的查询次数
        int[][] testCases = {
                {0, 20},    // 对齐情况
                {15, 10},   // 轻微不对齐
                {25, 30},   // 跨页情况
                {50, 5},    // 小批量
                {100, 100}  // 大批量
        };
        
        for (int[] testCase : testCases) {
            int offset = testCase[0];
            int limit = testCase[1];
            
            log.info("测试用例: offset={}, limit={}", offset, limit);
            
            // 计算需要的查询次数
            int queryCount = calculateQueryCount(offset, limit);
            log.info("  预计查询次数: {}", queryCount);
            
            // 计算数据传输效率
            double efficiency = calculateEfficiency(offset, limit);
            log.info("  数据传输效率: {:.1f}%", efficiency * 100);
        }
    }

    /**
     * 计算查询次数
     */
    private int calculateQueryCount(int offset, int limit) {
        int remaining = limit;
        int currentOffset = offset;
        int queryCount = 0;
        
        while (remaining > 0) {
            queryCount++;
            int pageSize = Math.min(remaining * 2, 100);
            int pageStartOffset = ((currentOffset / pageSize)) * pageSize;
            int validCount = Math.min(pageSize - (currentOffset - pageStartOffset), remaining);
            
            remaining -= validCount;
            currentOffset += validCount;
            
            if (validCount <= 0) break; // 防止无限循环
        }
        
        return queryCount;
    }

    /**
     * 计算数据传输效率
     */
    private double calculateEfficiency(int offset, int limit) {
        int totalTransferred = 0;
        int remaining = limit;
        int currentOffset = offset;
        
        while (remaining > 0) {
            int pageSize = Math.min(remaining * 2, 100);
            totalTransferred += pageSize;
            
            int pageStartOffset = ((currentOffset / pageSize)) * pageSize;
            int validCount = Math.min(pageSize - (currentOffset - pageStartOffset), remaining);
            
            remaining -= validCount;
            currentOffset += validCount;
            
            if (validCount <= 0) break;
        }
        
        return totalTransferred > 0 ? (double) limit / totalTransferred : 0;
    }

    /**
     * 运行所有测试
     */
    public void runAllTests() {
        try {
            testCdpPagingFix();
            testPerformanceComparison();
            
            log.info("=== CDP分页修复测试完成 ===");
        } catch (Exception e) {
            log.error("测试过程中发生错误", e);
        }
    }
}
