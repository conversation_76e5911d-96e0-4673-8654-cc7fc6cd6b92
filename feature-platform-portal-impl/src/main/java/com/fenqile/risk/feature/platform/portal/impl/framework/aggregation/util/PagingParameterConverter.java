package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.util;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy.AggregationStrategy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 分页参数转换器
 * 
 * 处理不同分页模式之间的参数转换
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Slf4j
public class PagingParameterConverter {

    /**
     * 将offset+limit转换为page+size
     * 
     * @param offset 偏移量
     * @param limit 限制数
     * @return 页码分页参数
     */
    public static PageSizeParam convertToPageSize(int offset, int limit) {
        if (limit <= 0) {
            throw new IllegalArgumentException("limit must be greater than 0");
        }
        
        int pageNo = (offset / limit) + 1;  // 页码从1开始
        int pageSize = limit;
        
        log.debug("转换分页参数: offset={}, limit={} -> pageNo={}, pageSize={}", 
                offset, limit, pageNo, pageSize);
        
        return new PageSizeParam(pageNo, pageSize);
    }

    /**
     * 将page+size转换为offset+limit
     * 
     * @param pageNo 页码（从1开始）
     * @param pageSize 页大小
     * @return 偏移量分页参数
     */
    public static OffsetLimitParam convertToOffsetLimit(int pageNo, int pageSize) {
        if (pageNo <= 0) {
            throw new IllegalArgumentException("pageNo must be greater than 0");
        }
        if (pageSize <= 0) {
            throw new IllegalArgumentException("pageSize must be greater than 0");
        }
        
        int offset = (pageNo - 1) * pageSize;
        int limit = pageSize;
        
        log.debug("转换分页参数: pageNo={}, pageSize={} -> offset={}, limit={}", 
                pageNo, pageSize, offset, limit);
        
        return new OffsetLimitParam(offset, limit);
    }

    /**
     * 根据策略的分页模式转换参数
     * 
     * @param strategy 策略
     * @param globalOffset 全局偏移量
     * @param fetchCount 获取数量
     * @return 转换后的分页参数
     */
    public static PagingParam convertForStrategy(AggregationStrategy<?> strategy, 
                                               int globalOffset, int fetchCount) {
        AggregationStrategy.PagingMode mode = strategy.getPagingMode();
        
        switch (mode) {
            case OFFSET_LIMIT:
                return new OffsetLimitParam(globalOffset, fetchCount);
                
            case PAGE_SIZE:
                PageSizeParam pageParam = convertToPageSize(globalOffset, fetchCount);
                return pageParam;
                
            case HYBRID:
                // 混合模式，提供两种参数
                PageSizeParam pageSizeParam = convertToPageSize(globalOffset, fetchCount);
                return new HybridParam(globalOffset, fetchCount, pageSizeParam.getPageNo(), pageSizeParam.getPageSize());
                
            default:
                log.warn("未知的分页模式: {}, 使用默认的OFFSET_LIMIT模式", mode);
                return new OffsetLimitParam(globalOffset, fetchCount);
        }
    }

    /**
     * 计算页码模式下的实际数据范围
     * 
     * 由于页码模式可能无法精确控制偏移量，需要计算实际获取的数据范围
     * 
     * @param requestOffset 请求的偏移量
     * @param requestLimit 请求的限制数
     * @param actualPageNo 实际查询的页码
     * @param actualPageSize 实际查询的页大小
     * @return 实际数据范围信息
     */
    public static ActualDataRange calculateActualRange(int requestOffset, int requestLimit,
                                                     int actualPageNo, int actualPageSize) {
        // 实际查询的起始偏移量
        int actualStartOffset = (actualPageNo - 1) * actualPageSize;
        
        // 实际查询的结束偏移量
        int actualEndOffset = actualStartOffset + actualPageSize;
        
        // 请求的结束偏移量
        int requestEndOffset = requestOffset + requestLimit;
        
        // 计算有效数据的起始位置（在实际查询结果中的索引）
        int validStartIndex = Math.max(0, requestOffset - actualStartOffset);
        
        // 计算有效数据的结束位置
        int validEndIndex = Math.min(actualPageSize, requestEndOffset - actualStartOffset);
        
        // 有效数据数量
        int validCount = Math.max(0, validEndIndex - validStartIndex);
        
        log.debug("计算实际数据范围: 请求[{},{}), 实际查询[{},{}), 有效范围[{},{}), 有效数量={}", 
                requestOffset, requestEndOffset, actualStartOffset, actualEndOffset,
                validStartIndex, validEndIndex, validCount);
        
        return new ActualDataRange(actualStartOffset, actualEndOffset, 
                                 validStartIndex, validEndIndex, validCount);
    }

    /**
     * 分页参数基类
     */
    @Data
    public static abstract class PagingParam {
        protected final String type;
        
        protected PagingParam(String type) {
            this.type = type;
        }
    }

    /**
     * 偏移量+限制数参数
     */
    @Data
    public static class OffsetLimitParam extends PagingParam {
        private final int offset;
        private final int limit;
        
        public OffsetLimitParam(int offset, int limit) {
            super("OFFSET_LIMIT");
            this.offset = offset;
            this.limit = limit;
        }
    }

    /**
     * 页码+页大小参数
     */
    @Data
    public static class PageSizeParam extends PagingParam {
        private final int pageNo;
        private final int pageSize;
        
        public PageSizeParam(int pageNo, int pageSize) {
            super("PAGE_SIZE");
            this.pageNo = pageNo;
            this.pageSize = pageSize;
        }
    }

    /**
     * 混合参数，同时包含两种模式的参数
     */
    @Data
    public static class HybridParam extends PagingParam {
        private final int offset;
        private final int limit;
        private final int pageNo;
        private final int pageSize;
        
        public HybridParam(int offset, int limit, int pageNo, int pageSize) {
            super("HYBRID");
            this.offset = offset;
            this.limit = limit;
            this.pageNo = pageNo;
            this.pageSize = pageSize;
        }
    }

    /**
     * 实际数据范围信息
     */
    @Data
    public static class ActualDataRange {
        private final int actualStartOffset;
        private final int actualEndOffset;
        private final int validStartIndex;
        private final int validEndIndex;
        private final int validCount;
        
        public ActualDataRange(int actualStartOffset, int actualEndOffset,
                             int validStartIndex, int validEndIndex, int validCount) {
            this.actualStartOffset = actualStartOffset;
            this.actualEndOffset = actualEndOffset;
            this.validStartIndex = validStartIndex;
            this.validEndIndex = validEndIndex;
            this.validCount = validCount;
        }
        
        /**
         * 是否有有效数据
         */
        public boolean hasValidData() {
            return validCount > 0;
        }
    }
}
