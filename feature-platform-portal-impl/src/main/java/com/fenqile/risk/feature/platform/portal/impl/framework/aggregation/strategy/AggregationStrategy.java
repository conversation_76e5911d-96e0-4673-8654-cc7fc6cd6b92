package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.strategy;

import com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model.AggregationPagingRequest;

import java.util.List;

/**
 * 聚合查询策略接口
 * 
 * 定义了聚合查询的核心方法，每个数据源需要实现此接口
 * 
 * @param <T> 查询结果类型
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface AggregationStrategy<T> {

    /**
     * 获取策略名称
     * 
     * @return 策略名称，用于日志和调试
     */
    String getStrategyName();

    /**
     * 获取数据源标识
     * 
     * @return 数据源标识
     */
    String getDataSource();

    /**
     * 获取策略优先级
     * 数值越小优先级越高，用于策略排序
     * 
     * @return 优先级数值
     */
    int getPriority();

    /**
     * 判断策略是否支持当前查询场景
     * 
     * @param request 查询请求
     * @return true表示支持，false表示不支持
     */
    boolean supports(AggregationPagingRequest request);

    /**
     * 计算符合条件的记录总数
     * 
     * @param request 查询请求
     * @return 记录总数
     */
    long count(AggregationPagingRequest request);

    /**
     * 分页查询数据
     * 
     * @param request 查询请求，包含分页参数
     * @return 查询结果列表
     */
    List<T> queryByPage(AggregationPagingRequest request);

    /**
     * 根据ID列表查询数据
     * 
     * @param request 查询请求
     * @param idList ID列表
     * @return 查询结果列表
     */
    default List<T> queryByIdList(AggregationPagingRequest request, List<String> idList) {
        throw new UnsupportedOperationException("queryByIdList not implemented");
    }

    /**
     * 获取策略配置信息
     * 
     * @return 配置信息
     */
    default StrategyConfig getConfig() {
        return StrategyConfig.defaultConfig();
    }

    /**
     * 策略配置类
     */
    class StrategyConfig {
        private boolean enableCache = false;
        private int cacheExpireSeconds = 300;
        private int maxRetryTimes = 3;
        private int timeoutSeconds = 30;

        public static StrategyConfig defaultConfig() {
            return new StrategyConfig();
        }

        // Getters and Setters
        public boolean isEnableCache() {
            return enableCache;
        }

        public StrategyConfig setEnableCache(boolean enableCache) {
            this.enableCache = enableCache;
            return this;
        }

        public int getCacheExpireSeconds() {
            return cacheExpireSeconds;
        }

        public StrategyConfig setCacheExpireSeconds(int cacheExpireSeconds) {
            this.cacheExpireSeconds = cacheExpireSeconds;
            return this;
        }

        public int getMaxRetryTimes() {
            return maxRetryTimes;
        }

        public StrategyConfig setMaxRetryTimes(int maxRetryTimes) {
            this.maxRetryTimes = maxRetryTimes;
            return this;
        }

        public int getTimeoutSeconds() {
            return timeoutSeconds;
        }

        public StrategyConfig setTimeoutSeconds(int timeoutSeconds) {
            this.timeoutSeconds = timeoutSeconds;
            return this;
        }
    }
}
