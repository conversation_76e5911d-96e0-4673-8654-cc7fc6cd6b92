package com.fenqile.risk.feature.platform.portal.impl.framework.aggregation.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聚合分页查询请求
 * 
 * 统一的查询请求模型，支持各种查询条件和分页参数
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
@Accessors(chain = true)
public class AggregationPagingRequest implements Cloneable {

    /**
     * 查询场景标识
     */
    private String sceneName;

    /**
     * 分页偏移量
     */
    private Integer offset = 0;

    /**
     * 分页大小
     */
    private Integer limit = 20;

    /**
     * 关键词模糊查询
     */
    private String keyword;

    /**
     * 精确匹配的名称列表
     */
    private List<String> nameList;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 状态过滤
     */
    private Integer status;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向 ASC/DESC
     */
    private String orderDirection = "DESC";

    /**
     * 扩展参数，用于特定场景的自定义参数
     */
    private Map<String, Object> extParams = new HashMap<>();

    /**
     * 时间范围查询 - 开始时间
     */
    private Long startTime;

    /**
     * 时间范围查询 - 结束时间
     */
    private Long endTime;

    /**
     * 创建带有新分页参数的请求副本
     * 
     * @param newOffset 新的偏移量
     * @param newLimit 新的分页大小
     * @return 新的请求对象
     */
    public AggregationPagingRequest cloneWithNewPaging(int newOffset, int newLimit) {
        try {
            AggregationPagingRequest cloned = (AggregationPagingRequest) this.clone();
            cloned.setOffset(newOffset);
            cloned.setLimit(newLimit);
            return cloned;
        } catch (CloneNotSupportedException e) {
            // 手动复制
            return new AggregationPagingRequest()
                    .setSceneName(this.sceneName)
                    .setOffset(newOffset)
                    .setLimit(newLimit)
                    .setKeyword(this.keyword)
                    .setNameList(this.nameList)
                    .setCategoryId(this.categoryId)
                    .setStatus(this.status)
                    .setOrderBy(this.orderBy)
                    .setOrderDirection(this.orderDirection)
                    .setExtParams(new HashMap<>(this.extParams))
                    .setStartTime(this.startTime)
                    .setEndTime(this.endTime);
        }
    }

    /**
     * 添加扩展参数
     * 
     * @param key 参数键
     * @param value 参数值
     * @return 当前对象，支持链式调用
     */
    public AggregationPagingRequest addExtParam(String key, Object value) {
        this.extParams.put(key, value);
        return this;
    }

    /**
     * 获取扩展参数
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @param <T> 参数类型
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtParam(String key, T defaultValue) {
        Object value = this.extParams.get(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return (T) value;
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 获取扩展参数
     * 
     * @param key 参数键
     * @param <T> 参数类型
     * @return 参数值，可能为null
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtParam(String key) {
        return (T) this.extParams.get(key);
    }

    /**
     * 验证分页参数
     * 
     * @return 验证结果
     */
    public boolean isValidPaging() {
        return offset != null && offset >= 0 && limit != null && limit > 0 && limit <= 1000;
    }

    /**
     * 规范化分页参数
     */
    public void normalizePaging() {
        if (offset == null || offset < 0) {
            offset = 0;
        }
        if (limit == null || limit <= 0) {
            limit = 20;
        }
        if (limit > 1000) {
            limit = 1000;
        }
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        AggregationPagingRequest cloned = (AggregationPagingRequest) super.clone();
        // 深拷贝扩展参数
        cloned.extParams = new HashMap<>(this.extParams);
        return cloned;
    }
}
